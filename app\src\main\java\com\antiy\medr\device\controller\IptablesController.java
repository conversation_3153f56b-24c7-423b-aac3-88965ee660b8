package com.antiy.medr.device.controller;

import android.util.Log;

import com.antiy.medr.device.DevInterfaceManager;
import com.antiy.medr.model.IptablesModel;
import com.antiy.medr.network.Api;
import com.antiy.medr.util.CrashHandler;
import com.blankj.utilcode.util.CrashUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.cetc30.auth.sdk.utils.ServerUtils;

import java.util.List;

/**
 * iptables控制
 * Created on 2022/9/21.
 *
 * <AUTHOR>
 */
public class IptablesController implements IController<IptablesModel> {
    private static List<IptablesModel> lastwhiteList = null;
    public static boolean is_operate=false;

    @Override
    public void enable() {

    }

    @Override
    public void disable() {

    }

    @Override
    public boolean setWhiteList(List<IptablesModel> whiteList) {

        if (lastwhiteList!=null && whiteList.size() == lastwhiteList.size()) {
            boolean isequals = true;
            for (IptablesModel iptablesModel : whiteList) {
                for (IptablesModel model : lastwhiteList) {
                    if (!(model.getIp_name().trim().equals(iptablesModel.getIp_name().trim())
                            && model.getIp_type().trim().equals(iptablesModel.getIp_type().trim())
                            && model.getProtocol().trim().equals(iptablesModel.getProtocol().trim())
                            && model.getSrc_type().trim().equals(iptablesModel.getSrc_type().trim())
                            && model.getSrc_ip().trim().equals(iptablesModel.getSrc_ip().trim())
                            && model.getSrc_ip_start().trim().equals(iptablesModel.getSrc_ip_start().trim())
                            && model.getSrc_ip_end().trim().equals(iptablesModel.getSrc_ip_end().trim())
                            && model.getSrc_port().trim().equals(iptablesModel.getSrc_port().trim())
                            && model.getDest_type().trim().equals(iptablesModel.getDest_type().trim())
                            && model.getDest_ip().trim().equals(iptablesModel.getDest_ip().trim())
                            && model.getDest_ip_start().trim().equals(iptablesModel.getDest_ip_start().trim())
                            && model.getDest_ip_end().trim().equals(iptablesModel.getDest_ip_end().trim())
                            && model.getDest_port().trim().equals(iptablesModel.getDest_port().trim()))) {
                        isequals = false;
                        break;
                    }
                }
            }
//判断值是否和上一次保存值相同,不同则加载
            if (!isequals) {
                loadWhiteList(whiteList);
            }

        }else {
            loadWhiteList(whiteList);
        }
        lastwhiteList = whiteList;
        return false;
    }

    public boolean loadWhiteList(List<IptablesModel> whiteList){
        boolean is_icmp = false;
        is_operate = true;
        try {
            //清空iptables
            DevInterfaceManager.getInstance()
                    .executeShellToSetIptables("-F");

            //设置服务器以及本地
            DevInterfaceManager.getInstance()
                    .executeShellToSetIptables("-P OUTPUT DROP");
            DevInterfaceManager.getInstance()
                    .executeShellToSetIptables("-P INPUT DROP");

            String ip = ServerUtils.getServerIp();

            if (ip != null) {
                DevInterfaceManager.getInstance()
                        .executeShellToSetIptables("-I INPUT -m tcp -p tcp -s " + ip + " -j ACCEPT");
                DevInterfaceManager.getInstance()
                        .executeShellToSetIptables("-I OUTPUT -m tcp -p tcp -d " + ip + " -j ACCEPT");
            }

            DevInterfaceManager.getInstance()
                    .executeShellToSetIptables("-I INPUT -m tcp -p tcp -s 127.0.0.1 -j ACCEPT");
            DevInterfaceManager.getInstance()
                    .executeShellToSetIptables("-I OUTPUT -m tcp -p tcp -d 127.0.0.1 -j ACCEPT");


            //加载新列表
            //  String exec = null;

            try {
                for (IptablesModel iptablesModel : whiteList) {

                    if ((iptablesModel.getIp_type().trim().equals("inbound") && iptablesModel.getSrc_type().trim().equals("single")) || (iptablesModel.getIp_type().trim().equals("outbound") && iptablesModel.getDest_type().trim().equals("single"))) {
                        //单个

                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("-I ");
                        if (iptablesModel.getIp_type().trim().equals("inbound")) {
                            stringBuilder.append("INPUT ");

                        } else if (iptablesModel.getIp_type().trim().equals("outbound")) {
                            stringBuilder.append("OUTPUT ");

                        }

                        if (iptablesModel.getProtocol().trim().equals("icmp")) {
                            is_icmp = true;
                            stringBuilder.append("-p icmp ");
                        } else if (iptablesModel.getProtocol().trim().equals("tcp")) {
                            stringBuilder.append("-m tcp -p tcp ");
                        } else if (iptablesModel.getProtocol().trim().equals("udp")) {
                            stringBuilder.append("-m udp -p udp ");
                        }

                        if (!iptablesModel.getDest_ip().trim().equals("")) {
                            stringBuilder.append("-d ").append(iptablesModel.getDest_ip().trim()).append(" ");
                        } else if (!iptablesModel.getSrc_ip().trim().equals("")) {
                            stringBuilder.append("-s ").append(iptablesModel.getSrc_ip().trim()).append(" ");
                        }

                        if (!iptablesModel.getDest_port().trim().equals("")) {
                            stringBuilder.append("--dport ").append(iptablesModel.getDest_port().trim()).append(" ");
                        }
                        if (!iptablesModel.getSrc_port().trim().equals("")) {
                            stringBuilder.append("--sport ").append(iptablesModel.getSrc_port().trim()).append(" ");
                        }
                        stringBuilder.append("-j ACCEPT");

                        DevInterfaceManager.getInstance()
                                .executeShellToSetIptables(stringBuilder.toString());

                        //   exec = stringBuilder.toString();
                    } else if ((iptablesModel.getIp_type().trim().equals("inbound") && iptablesModel.getSrc_type().trim().equals("multiple")) || (iptablesModel.getIp_type().trim().equals("outbound") && iptablesModel.getDest_type().trim().equals("multiple"))) {
                        //区间

                        StringBuilder stringBuilder = new StringBuilder();
                        stringBuilder.append("-I ");
                        if (iptablesModel.getIp_type().trim().equals("inbound")) {
                            stringBuilder.append("INPUT ");

                        } else if (iptablesModel.getIp_type().trim().equals("outbound")) {
                            stringBuilder.append("OUTPUT ");

                        }

                        if (iptablesModel.getProtocol().trim().equals("icmp")) {
                            is_icmp = true;
                            stringBuilder.append("-p icmp ");
                        } else if (iptablesModel.getProtocol().trim().equals("tcp")) {
                            stringBuilder.append("-m tcp -p tcp ");
                        } else if (iptablesModel.getProtocol().trim().equals("udp")) {
                            stringBuilder.append("-m udp -p udp ");
                        }

                        if (!iptablesModel.getSrc_ip_start().trim().equals("")) {
                            stringBuilder.append("-m iprange --src-range ").append(iptablesModel.getSrc_ip_start().trim()).append("-").append(iptablesModel.getSrc_ip_end().trim()).append(" ");
                        } else if (!iptablesModel.getDest_ip_start().trim().equals("")) {
                            stringBuilder.append("-m iprange --dst-range ").append(iptablesModel.getDest_ip_start().trim()).append("-").append(iptablesModel.getDest_ip_end().trim()).append(" ");
                        }

                        if (!iptablesModel.getDest_port().trim().equals("")) {
                            stringBuilder.append("--dport ").append(iptablesModel.getDest_port().trim()).append(" ");
                        }
                        if (!iptablesModel.getSrc_port().trim().equals("")) {
                            stringBuilder.append("--sport ").append(iptablesModel.getSrc_port().trim()).append(" ");
                        }
                        stringBuilder.append("-j ACCEPT");

                        DevInterfaceManager.getInstance()
                                .executeShellToSetIptables(stringBuilder.toString());

                        //                  exec = stringBuilder.toString();
                    }
//                CrashHandler.addlog(exec);
//                CrashHandler.addlog(iptablesModel.toString());
                }
            } catch (Exception e) {
                e.printStackTrace();
                //CrashHandler.addlog(Log.getStackTraceString(e));
            }

            DevInterfaceManager.getInstance().executeShellToSetIptables("-I INPUT -m state --state RELATED,ESTABLISHED -j ACCEPT");
            DevInterfaceManager.getInstance().executeShellToSetIptables("-I OUTPUT -m state --state RELATED,ESTABLISHED -j ACCEPT");
            is_operate=false;
        }catch (Exception e){
            e.printStackTrace();
            is_operate =false;
        }


        return true;
    }
}
