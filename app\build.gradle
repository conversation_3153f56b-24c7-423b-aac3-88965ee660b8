plugins {
    id 'com.android.application'
}

android {
    namespace 'com.antiy.medr'
    compileSdk 34

    defaultConfig {
        applicationId "com.antiy.medr"
        minSdkVersion 23
        //noinspection ExpiredTargetSdkVersion
        targetSdk 23
        versionCode 2
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters "armeabi",'arm64-v8a'
        }
    }

    signingConfigs {
        release {
            storeFile file('../AnalysisTest.keystore')
            storePassword '123456'
            keyAlias 'AnalysisTest'
            keyPassword '123456'
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.release
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release

            //修改 apk 的 文件名
            applicationVariants.all {
                variant ->
                    variant.outputs.all { output ->
                        output.outputFileName = "antiy-medr-${buildType.name}-${releaseTime()}.apk"
                    }
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildFeatures {
        viewBinding = true
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
            assets {
                srcDirs 'src\\main\\assets'
            }
        }
    }
//    buildToolsVersion '30.0.2'

//    ndkVersion '21.1.6352462'
}

static def releaseTime() {
    return new Date().format("yyyyMMddHHmmss", TimeZone.getTimeZone("GMT+08:00"))
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])
    implementation files('libs/cetc30auth-sdk_v0.0.0.9_23120111_release.aar')

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test:rules:1.3.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    implementation project(':xplugin')

    implementation 'com.squareup.okhttp3:okhttp:4.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:3.5.0'
    implementation 'com.google.code.gson:gson:2.10.1'

//    implementation 'org.xutils:xutils:3.9.0'
//    implementation 'org.xplugin:xplugin:1.3.10'

    //rx
    implementation 'io.reactivex.rxjava3:rxjava:3.1.5'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'
    implementation 'com.github.liujingxing.rxlife:rxlife-rxjava3:2.2.1'
    implementation 'com.jakewharton.rxbinding4:rxbinding:4.0.0'

    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.2'
    implementation 'com.jakewharton.timber:timber:4.7.1'
    implementation 'com.blankj:utilcodex:1.30.6'

//    implementation 'com.github.tbruyelle:rxpermissions:0.12'

    //database
    def room_version = "2.4.2"
    //noinspection GradleDependency
    implementation "androidx.room:room-runtime:$room_version"
    annotationProcessor "androidx.room:room-compiler:$room_version"
    //noinspection GradleDependency
    implementation "androidx.room:room-rxjava3:$room_version"

    implementation 'com.alibaba:fastjson:1.1.46.android'
}
//repositories {
//    flatDir {
//        dirs 'libs'
//    }
//}
