apply plugin: 'com.android.library'

android {

    compileSdk 34
//    buildToolsVersion '30.0.2'
    namespace 'org.xplugin.core'
    lintOptions {
        abortOnError false
    }

    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 30
        versionCode 20200824
        versionName version

        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        debug {
            minifyEnabled false
        }
        release {
            minifyEnabled false
        }
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api 'org.xutils:xutils:3.9.0'
}

task sourcesJar(type: Jar) {
    from android.sourceSets.main.java.srcDirs
//    classifier = 'sources'
}
task javadoc(type: Javadoc) {
    options.encoding = "UTF-8"
    source = android.sourceSets.main.java.srcDirs
    classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
}
task javadocJar(type: Jar, dependsOn: javadoc) {
//    classifier = 'javadoc'
    from javadoc.destinationDir
}
artifacts {
    archives javadocJar
    archives sourcesJar
}
Properties properties = new Properties()
properties.load(project.rootProject.file('local.properties').newDataInputStream())
