-- Merging decision tree log ---
manifest
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:2:1-143:12
INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:2:1-143:12
INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:2:1-143:12
INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:2:1-143:12
MERGED from [cetc30auth-sdk_v0.0.0.9_23120111_release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad15b42b2a8bc9c5ea9a6b522707ac4a\transformed\jetified-cetc30auth-sdk_v0.0.0.9_23120111_release\AndroidManifest.xml:2:1-14:12
MERGED from [com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\1991843d7e113af8537127745aba1464\transformed\jetified-BaseRecyclerViewAdapterHelper-3.0.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.databinding:databinding-adapters:3.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f556d5d62356a770c8672a4d0b0655c\transformed\databinding-adapters-3.6.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.databinding:databinding-runtime:3.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\11b2a3cf55cebe554551344c197db918\transformed\databinding-runtime-3.6.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.databinding:viewbinding:8.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\17106141f493c6e2f0fc839ee56425d0\transformed\jetified-viewbinding-8.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1feccac8d95f313480f0368aa8e018d4\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f9d625ec4482bc248d849dc9feeca11b\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\59adbd0959165cbbea264d815c34e4db\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\57c6a5b6246a0d7b150fa4e3178ac9f1\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-200:12
MERGED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:2:1-41:12
MERGED from [com.jakewharton.rxbinding4:rxbinding:4.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\833ee6713ae370b96f1f0e50d2e4e05f\transformed\jetified-rxbinding-4.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.reactivex.rxjava3:rxandroid:3.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7b8c2856e0dd021ce75857f2b5eb7ecb\transformed\jetified-rxandroid-3.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.room:room-rxjava3:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\8032fea10f68229d42de876ffa779144\transformed\jetified-room-rxjava3-2.4.2\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.liujingxing.rxlife:rxlife-rxjava3:2.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3595a71465262547071769f0b2061cc3\transformed\jetified-rxlife-rxjava3-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.jakewharton.timber:timber:4.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b4fef9d693e7ef2b3b6b66cabf3f2e59\transformed\jetified-timber-4.7.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4cdf35959b1d7c5ea8be7c4de5729b\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\7760abb4e38850b2ade857af2b684b95\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\713f06060bb21d0f1bcfe255d06a4e4e\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4cbe54c223adb563945006351332a077\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de8b47aabebfff1d74c477fd7c8f776f\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\acd9b5204161d17223eeb0dda734ea01\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46ede0b12f19bb734ba20afe30dd0ab7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3e9c260c5e545bf829a36e8fdfc3ec8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8f614290d9ebcf84564dab769ddc576b\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\007bf9a0fd30226cc68f28752a03b4c6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a2e3fb090944c935d396d80de22c399\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c31c17f688cfac4b583cfbf69b8d6b8f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\3cee2045574b6f7a39368740a69f1314\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f917c921aada2b6cf1de100a3a7bd723\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9456a301715c410c59a99c39ea2930ae\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ef04fd9a5b8fe116d8cf3f7920a09027\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c6ac01da394ddd8842843341bfdcf31\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a43e8d6e4b402d8d3cb4443ee6613e9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\948a12f3848d6c41c88714c6432a28c9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a497f2e3d3a77c08b9be83a7eef0196\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fab540ea1dd0131b5bad36431a1106a1\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99d3db3008e91e015e2773a671a9b72b\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5cd1d639083433bb2d32b757397220b7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\224cb338d3c5f51cc6f3cf595a27a88b\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e1ce0133919413b9aad79b71783663df\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f6c1af0d43f2acc86055b1ffcd5c81ad\transformed\sqlite-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2767494b34a79d1bf41cda8a09604415\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a960214c9e110311baec51583aade570\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d7e15c4d2f3299bed7768d772dd92e8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\157dc294aaff7bbbe4577b1149ae14bb\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d85fd36fa3667373746251e6d07041\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\06cbd520291ba5a2fb78cef16a9e54d1\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\56e1a0a328d351f92da57159e678f2f2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7f137be30ba145856d430118efcfecd1\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d23e6eda4cf8405f5c15ed402ecf716\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [org.xutils:xutils:3.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3dbd9d70d5a66b368c3e9d293f54f2a4\transformed\jetified-xutils-3.9.0\AndroidManifest.xml:2:1-11:12
INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:2:1-143:12
INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:2:1-143:12
INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:2:1-143:12
	package
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:4:5-29
		INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml
		INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:2:1-143:12
		INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:2:1-143:12
		INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:2:11-69
uses-sdk
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:6:5-8:55
INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:6:5-8:55
INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:6:5-8:55
MERGED from [cetc30auth-sdk_v0.0.0.9_23120111_release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad15b42b2a8bc9c5ea9a6b522707ac4a\transformed\jetified-cetc30auth-sdk_v0.0.0.9_23120111_release\AndroidManifest.xml:7:5-9:41
MERGED from [cetc30auth-sdk_v0.0.0.9_23120111_release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad15b42b2a8bc9c5ea9a6b522707ac4a\transformed\jetified-cetc30auth-sdk_v0.0.0.9_23120111_release\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\1991843d7e113af8537127745aba1464\transformed\jetified-BaseRecyclerViewAdapterHelper-3.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\1991843d7e113af8537127745aba1464\transformed\jetified-BaseRecyclerViewAdapterHelper-3.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.databinding:databinding-adapters:3.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f556d5d62356a770c8672a4d0b0655c\transformed\databinding-adapters-3.6.1\AndroidManifest.xml:22:5-44
MERGED from [androidx.databinding:databinding-adapters:3.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f556d5d62356a770c8672a4d0b0655c\transformed\databinding-adapters-3.6.1\AndroidManifest.xml:22:5-44
MERGED from [androidx.databinding:databinding-runtime:3.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\11b2a3cf55cebe554551344c197db918\transformed\databinding-runtime-3.6.1\AndroidManifest.xml:22:5-44
MERGED from [androidx.databinding:databinding-runtime:3.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\11b2a3cf55cebe554551344c197db918\transformed\databinding-runtime-3.6.1\AndroidManifest.xml:22:5-44
MERGED from [androidx.databinding:viewbinding:8.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\17106141f493c6e2f0fc839ee56425d0\transformed\jetified-viewbinding-8.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\17106141f493c6e2f0fc839ee56425d0\transformed\jetified-viewbinding-8.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1feccac8d95f313480f0368aa8e018d4\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1feccac8d95f313480f0368aa8e018d4\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f9d625ec4482bc248d849dc9feeca11b\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f9d625ec4482bc248d849dc9feeca11b\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\59adbd0959165cbbea264d815c34e4db\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\59adbd0959165cbbea264d815c34e4db\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\57c6a5b6246a0d7b150fa4e3178ac9f1\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\57c6a5b6246a0d7b150fa4e3178ac9f1\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:7:5-44
MERGED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:7:5-44
MERGED from [com.jakewharton.rxbinding4:rxbinding:4.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\833ee6713ae370b96f1f0e50d2e4e05f\transformed\jetified-rxbinding-4.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton.rxbinding4:rxbinding:4.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\833ee6713ae370b96f1f0e50d2e4e05f\transformed\jetified-rxbinding-4.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.reactivex.rxjava3:rxandroid:3.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7b8c2856e0dd021ce75857f2b5eb7ecb\transformed\jetified-rxandroid-3.0.0\AndroidManifest.xml:18:5-43
MERGED from [io.reactivex.rxjava3:rxandroid:3.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7b8c2856e0dd021ce75857f2b5eb7ecb\transformed\jetified-rxandroid-3.0.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.room:room-rxjava3:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\8032fea10f68229d42de876ffa779144\transformed\jetified-room-rxjava3-2.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-rxjava3:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\8032fea10f68229d42de876ffa779144\transformed\jetified-room-rxjava3-2.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.liujingxing.rxlife:rxlife-rxjava3:2.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3595a71465262547071769f0b2061cc3\transformed\jetified-rxlife-rxjava3-2.2.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.liujingxing.rxlife:rxlife-rxjava3:2.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3595a71465262547071769f0b2061cc3\transformed\jetified-rxlife-rxjava3-2.2.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.jakewharton.timber:timber:4.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b4fef9d693e7ef2b3b6b66cabf3f2e59\transformed\jetified-timber-4.7.1\AndroidManifest.xml:5:5-43
MERGED from [com.jakewharton.timber:timber:4.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b4fef9d693e7ef2b3b6b66cabf3f2e59\transformed\jetified-timber-4.7.1\AndroidManifest.xml:5:5-43
MERGED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4cdf35959b1d7c5ea8be7c4de5729b\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4cdf35959b1d7c5ea8be7c4de5729b\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\7760abb4e38850b2ade857af2b684b95\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\7760abb4e38850b2ade857af2b684b95\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\713f06060bb21d0f1bcfe255d06a4e4e\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\713f06060bb21d0f1bcfe255d06a4e4e\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4cbe54c223adb563945006351332a077\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4cbe54c223adb563945006351332a077\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de8b47aabebfff1d74c477fd7c8f776f\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\de8b47aabebfff1d74c477fd7c8f776f\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\acd9b5204161d17223eeb0dda734ea01\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\acd9b5204161d17223eeb0dda734ea01\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46ede0b12f19bb734ba20afe30dd0ab7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\46ede0b12f19bb734ba20afe30dd0ab7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3e9c260c5e545bf829a36e8fdfc3ec8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f3e9c260c5e545bf829a36e8fdfc3ec8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8f614290d9ebcf84564dab769ddc576b\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8f614290d9ebcf84564dab769ddc576b\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\007bf9a0fd30226cc68f28752a03b4c6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\007bf9a0fd30226cc68f28752a03b4c6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a2e3fb090944c935d396d80de22c399\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9a2e3fb090944c935d396d80de22c399\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c31c17f688cfac4b583cfbf69b8d6b8f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c31c17f688cfac4b583cfbf69b8d6b8f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\3cee2045574b6f7a39368740a69f1314\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\3cee2045574b6f7a39368740a69f1314\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f917c921aada2b6cf1de100a3a7bd723\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\f917c921aada2b6cf1de100a3a7bd723\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9456a301715c410c59a99c39ea2930ae\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9456a301715c410c59a99c39ea2930ae\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ef04fd9a5b8fe116d8cf3f7920a09027\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ef04fd9a5b8fe116d8cf3f7920a09027\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c6ac01da394ddd8842843341bfdcf31\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c6ac01da394ddd8842843341bfdcf31\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a43e8d6e4b402d8d3cb4443ee6613e9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a43e8d6e4b402d8d3cb4443ee6613e9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\948a12f3848d6c41c88714c6432a28c9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\948a12f3848d6c41c88714c6432a28c9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a497f2e3d3a77c08b9be83a7eef0196\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a497f2e3d3a77c08b9be83a7eef0196\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fab540ea1dd0131b5bad36431a1106a1\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fab540ea1dd0131b5bad36431a1106a1\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99d3db3008e91e015e2773a671a9b72b\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99d3db3008e91e015e2773a671a9b72b\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5cd1d639083433bb2d32b757397220b7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5cd1d639083433bb2d32b757397220b7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\224cb338d3c5f51cc6f3cf595a27a88b\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\224cb338d3c5f51cc6f3cf595a27a88b\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e1ce0133919413b9aad79b71783663df\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e1ce0133919413b9aad79b71783663df\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f6c1af0d43f2acc86055b1ffcd5c81ad\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f6c1af0d43f2acc86055b1ffcd5c81ad\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2767494b34a79d1bf41cda8a09604415\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2767494b34a79d1bf41cda8a09604415\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a960214c9e110311baec51583aade570\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a960214c9e110311baec51583aade570\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d7e15c4d2f3299bed7768d772dd92e8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d7e15c4d2f3299bed7768d772dd92e8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\157dc294aaff7bbbe4577b1149ae14bb\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\157dc294aaff7bbbe4577b1149ae14bb\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d85fd36fa3667373746251e6d07041\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d85fd36fa3667373746251e6d07041\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\06cbd520291ba5a2fb78cef16a9e54d1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\06cbd520291ba5a2fb78cef16a9e54d1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\56e1a0a328d351f92da57159e678f2f2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\56e1a0a328d351f92da57159e678f2f2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7f137be30ba145856d430118efcfecd1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7f137be30ba145856d430118efcfecd1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d23e6eda4cf8405f5c15ed402ecf716\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4d23e6eda4cf8405f5c15ed402ecf716\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [org.xutils:xutils:3.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3dbd9d70d5a66b368c3e9d293f54f2a4\transformed\jetified-xutils-3.9.0\AndroidManifest.xml:7:5-44
MERGED from [org.xutils:xutils:3.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3dbd9d70d5a66b368c3e9d293f54f2a4\transformed\jetified-xutils-3.9.0\AndroidManifest.xml:7:5-44
INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:6:5-8:55
INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:6:5-8:55
	tools:overrideLibrary
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:8:9-52
	android:targetSdkVersion
		INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:6:5-8:55
		INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:7:9-35
		INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml
		INJECTED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:10:5-79
MERGED from [cetc30auth-sdk_v0.0.0.9_23120111_release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad15b42b2a8bc9c5ea9a6b522707ac4a\transformed\jetified-cetc30auth-sdk_v0.0.0.9_23120111_release\AndroidManifest.xml:11:5-79
MERGED from [cetc30auth-sdk_v0.0.0.9_23120111_release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad15b42b2a8bc9c5ea9a6b522707ac4a\transformed\jetified-cetc30auth-sdk_v0.0.0.9_23120111_release\AndroidManifest.xml:11:5-79
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:10:22-76
uses-permission#android.permission.INTERNET
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:11:5-67
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:11:22-64
uses-permission#android.permission.REORDER_TASKS
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:12:5-72
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:12:22-69
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:13:5-81
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:13:22-78
uses-permission#ndroid.permission.MANAGE_DEVICE_ADMINS
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:14:5-78
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:14:22-75
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:15:5-80
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:15:22-77
uses-permission#android.permission.DEVICE_POWER
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:16:5-18:47
	tools:ignore
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:18:9-44
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:17:9-55
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:19:5-75
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:19:22-72
uses-permission#android.permission.KILL_BACKGROUND_PROCESSES
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:20:5-84
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:20:22-81
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:21:5-68
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:21:22-65
uses-permission#android.permission.WRITE_SETTINGS
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:22:5-73
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:22:22-70
uses-permission#android.permission.CHANGE_CONFIGURATION
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:23:5-79
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:23:22-76
uses-permission#android.permission.WRITE_SECURE_SETTINGS
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:24:5-80
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:24:22-77
uses-permission#android.permission.USES_POLICY_FORCE_LOCK
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:25:5-81
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:25:22-78
uses-permission#android.permission.BLUETOOTH
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:26:5-68
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:26:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:27:5-74
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:27:22-71
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:28:5-77
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:28:22-74
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:29:5-78
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:29:22-75
uses-permission#android.permission.CAMERA
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:30:5-65
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:30:22-62
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:31:5-76
MERGED from [cetc30auth-sdk_v0.0.0.9_23120111_release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad15b42b2a8bc9c5ea9a6b522707ac4a\transformed\jetified-cetc30auth-sdk_v0.0.0.9_23120111_release\AndroidManifest.xml:12:5-76
MERGED from [cetc30auth-sdk_v0.0.0.9_23120111_release.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad15b42b2a8bc9c5ea9a6b522707ac4a\transformed\jetified-cetc30auth-sdk_v0.0.0.9_23120111_release\AndroidManifest.xml:12:5-76
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:31:22-73
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:32:5-79
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:32:22-76
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:33:5-76
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:33:22-73
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:34:5-95
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:34:22-92
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:36:5-37:47
	tools:ignore
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:37:9-44
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:36:22-82
application
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:40:5-141:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1feccac8d95f313480f0368aa8e018d4\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1feccac8d95f313480f0368aa8e018d4\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f9d625ec4482bc248d849dc9feeca11b\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f9d625ec4482bc248d849dc9feeca11b\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-198:19
MERGED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-198:19
MERGED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:9:5-39:19
MERGED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:9:5-39:19
MERGED from [com.jakewharton.timber:timber:4.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b4fef9d693e7ef2b3b6b66cabf3f2e59\transformed\jetified-timber-4.7.1\AndroidManifest.xml:7:5-20
MERGED from [com.jakewharton.timber:timber:4.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b4fef9d693e7ef2b3b6b66cabf3f2e59\transformed\jetified-timber-4.7.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a960214c9e110311baec51583aade570\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a960214c9e110311baec51583aade570\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d7e15c4d2f3299bed7768d772dd92e8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d7e15c4d2f3299bed7768d772dd92e8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [org.xutils:xutils:3.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3dbd9d70d5a66b368c3e9d293f54f2a4\transformed\jetified-xutils-3.9.0\AndroidManifest.xml:9:5-20
MERGED from [org.xutils:xutils:3.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3dbd9d70d5a66b368c3e9d293f54f2a4\transformed\jetified-xutils-3.9.0\AndroidManifest.xml:9:5-20
	android:extractNativeLibs
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:44:9-41
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:48:9-35
	android:label
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:46:9-41
	android:roundIcon
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:47:9-54
	android:icon
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:45:9-43
	android:allowBackup
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:42:9-36
	android:theme
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:49:9-42
	android:directBootAware
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:43:9-39
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:41:9-32
service#com.antiy.medr.service.KeepAliveService
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:50:9-57:19
	android:enabled
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:52:13-35
	android:exported
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:53:13-36
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:51:13-53
intent-filter#action:name:com.antiy.medr.service.KeepAliveService
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:54:13-56:29
action#com.antiy.medr.service.KeepAliveService
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:55:17-82
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:55:25-79
service#com.antiy.medr.device.LockScreenService
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:58:9-61:39
	android:enabled
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:60:13-35
	android:exported
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:61:13-36
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:59:13-53
receiver#com.antiy.medr.auth.AuthReceiver
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:63:9-72:20
	android:enabled
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:65:13-35
	android:exported
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:66:13-36
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:64:13-46
intent-filter#action:name:android.intent.action.SCREEN_ON+action:name:com.cetc30.auth.login+action:name:com.cetc30.auth.logout
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:67:13-71:29
action#android.intent.action.SCREEN_ON
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:68:17-74
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:68:25-71
action#com.cetc30.auth.logout
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:69:17-65
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:69:25-62
action#com.cetc30.auth.login
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:70:17-64
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:70:25-61
activity#com.antiy.medr.ui.SplashActivity
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:74:9-76:39
	android:exported
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:76:13-36
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:75:13-46
activity#com.antiy.medr.ui.TerminalWatchActivity
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:77:9-82:54
	android:screenOrientation
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:82:13-51
	android:label
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:81:13-62
	android:exported
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:80:13-37
	android:configChanges
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:79:13-74
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:78:13-53
activity#com.antiy.medr.ui.MainActivity
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:83:9-94:20
	android:screenOrientation
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:88:13-51
	android:launchMode
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:87:13-44
	android:exported
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:86:13-36
	android:configChanges
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:85:13-74
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:84:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:89:13-93:29
action#android.intent.action.MAIN
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:90:17-69
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:90:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:92:17-77
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:92:27-74
receiver#com.antiy.medr.device.AdminReceiver
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:111:9-123:20
	android:label
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:114:13-45
	android:permission
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:115:13-70
	android:configChanges
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:113:13-74
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:112:13-49
meta-data#android.app.device_admin
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:116:13-118:56
	android:resource
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:118:17-53
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:117:17-56
intent-filter#action:name:android.app.action.DEVICE_ADMIN_ENABLED
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:120:13-122:29
action#android.app.action.DEVICE_ADMIN_ENABLED
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:121:17-82
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:121:25-79
activity#com.cetc30.auth.sdk.api.AgentActivity
ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:137:9-139:39
	android:exported
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:139:13-36
	android:name
		ADDED from D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:138:13-65
service#org.xplugin.core.app.ServicesProxy
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-70
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-67
provider#org.xplugin.core.app.ContentProviderProxy
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-14:50
	android:grantUriPermissions
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-47
	android:authorities
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-68
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-36
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-69
activity#org.xplugin.tpl.DefaultActivity
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-20:45
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-42
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-59
activity#org.xplugin.tpl.DefaultActivity_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-26:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:13-42
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-61
activity#org.xplugin.tpl.SingleTopActivity_1
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-33:46
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:33:13-43
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:32:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-63
activity#org.xplugin.tpl.SingleTopActivity_2
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:9-38:46
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:13-43
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:13-63
activity#org.xplugin.tpl.SingleTopActivity_3
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:9-43:46
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-43
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:13-63
activity#org.xplugin.tpl.SingleTopActivity_4
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:9-48:46
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:48:13-43
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:47:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-63
activity#org.xplugin.tpl.SingleTopActivity_5
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:49:9-53:46
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:53:13-43
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:52:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:51:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:50:13-63
activity#org.xplugin.tpl.SingleTopActivity_1_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:54:9-59:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:58:13-43
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:57:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:56:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:59:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:55:13-65
activity#org.xplugin.tpl.SingleTopActivity_2_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:60:9-65:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:64:13-43
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:63:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:62:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:65:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:61:13-65
activity#org.xplugin.tpl.SingleTopActivity_3_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:66:9-71:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:70:13-43
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:69:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:71:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:67:13-65
activity#org.xplugin.tpl.SingleTopActivity_4_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:72:9-77:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:76:13-43
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:75:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:74:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:73:13-65
activity#org.xplugin.tpl.SingleTopActivity_5_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:78:9-83:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:82:13-43
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:81:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:80:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:83:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:79:13-65
activity#org.xplugin.tpl.SingleTaskActivity_1
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:86:9-90:47
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:90:13-44
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:89:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:88:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:87:13-64
activity#org.xplugin.tpl.SingleTaskActivity_2
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:91:9-95:47
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:95:13-44
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:94:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:93:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:92:13-64
activity#org.xplugin.tpl.SingleTaskActivity_3
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:96:9-100:47
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:100:13-44
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:99:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:98:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:97:13-64
activity#org.xplugin.tpl.SingleTaskActivity_4
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:101:9-105:47
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:105:13-44
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:104:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:103:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:102:13-64
activity#org.xplugin.tpl.SingleTaskActivity_5
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:106:9-110:47
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:110:13-44
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:109:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:108:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:107:13-64
activity#org.xplugin.tpl.SingleTaskActivity_1_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:111:9-116:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:115:13-44
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:114:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:113:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:116:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:112:13-66
activity#org.xplugin.tpl.SingleTaskActivity_2_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:117:9-122:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:121:13-44
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:120:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:119:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:122:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:118:13-66
activity#org.xplugin.tpl.SingleTaskActivity_3_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:123:9-128:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:127:13-44
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:126:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:125:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:128:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:124:13-66
activity#org.xplugin.tpl.SingleTaskActivity_4_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:129:9-134:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:133:13-44
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:132:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:131:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:134:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:130:13-66
activity#org.xplugin.tpl.SingleTaskActivity_5_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:135:9-140:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:139:13-44
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:138:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:137:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:140:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:136:13-66
activity#org.xplugin.tpl.SingleInstanceActivity_1
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:143:9-147:51
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:147:13-48
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:146:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:145:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:144:13-68
activity#org.xplugin.tpl.SingleInstanceActivity_2
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:148:9-152:51
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:152:13-48
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:151:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:150:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:149:13-68
activity#org.xplugin.tpl.SingleInstanceActivity_3
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:153:9-157:51
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:157:13-48
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:156:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:155:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:154:13-68
activity#org.xplugin.tpl.SingleInstanceActivity_4
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:158:9-162:51
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:162:13-48
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:161:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:160:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:159:13-68
activity#org.xplugin.tpl.SingleInstanceActivity_5
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:163:9-167:51
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:167:13-48
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:166:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:165:13-74
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:164:13-68
activity#org.xplugin.tpl.SingleInstanceActivity_1_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:168:9-173:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:172:13-48
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:171:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:170:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:173:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:169:13-70
activity#org.xplugin.tpl.SingleInstanceActivity_2_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:174:9-179:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:178:13-48
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:177:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:176:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:179:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:175:13-70
activity#org.xplugin.tpl.SingleInstanceActivity_3_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:180:9-185:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:184:13-48
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:183:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:182:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:185:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:181:13-70
activity#org.xplugin.tpl.SingleInstanceActivity_4_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:186:9-191:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:190:13-48
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:189:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:188:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:191:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:187:13-70
activity#org.xplugin.tpl.SingleInstanceActivity_5_t
ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:192:9-197:64
	android:launchMode
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:196:13-48
	android:exported
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:195:13-37
	android:configChanges
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:194:13-74
	android:theme
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:197:13-61
	android:name
		ADDED from [:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:193:13-70
activity#com.blankj.utilcode.util.UtilsTransActivity4MainProcess
ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:10:9-14:75
	android:windowSoftInputMode
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:14:13-72
	android:configChanges
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:12:13-74
	android:theme
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:13:13-55
	android:name
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:11:13-83
activity#com.blankj.utilcode.util.UtilsTransActivity
ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:15:9-20:75
	android:multiprocess
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:18:13-40
	android:windowSoftInputMode
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:20:13-72
	android:configChanges
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:17:13-74
	android:theme
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:19:13-55
	android:name
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:16:13-71
provider#com.blankj.utilcode.util.UtilsFileProvider
ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:22:9-30:20
	android:grantUriPermissions
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:26:13-47
	android:authorities
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:24:13-73
	android:exported
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:23:13-70
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:27:13-29:68
	android:resource
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:29:17-65
	android:name
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:28:17-67
service#com.blankj.utilcode.util.MessengerUtils$ServerService
ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:32:9-38:19
	android:exported
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:33:13-81
intent-filter#action:name:${applicationId}.messenger
ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:35:13-37:29
intent-filter#action:name:com.antiy.medr.messenger
ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:35:13-37:29
action#${applicationId}.messenger
ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:36:25-66
action#com.antiy.medr.messenger
ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:36:25-66
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:26:13-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d7e15c4d2f3299bed7768d772dd92e8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d7e15c4d2f3299bed7768d772dd92e8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.antiy.medr.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.antiy.medr.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
