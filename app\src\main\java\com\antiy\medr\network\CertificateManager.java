package com.antiy.medr.network;

import android.content.Context;

import com.antiy.medr.Constants;

import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;

import timber.log.Timber;

/**
 * SSL证书管理器
 * 负责SSL证书的加载、验证和管理
 * 
 * <AUTHOR>
 * @date 2024/12/24
 */
public class CertificateManager {
    private static final String TAG = "CertificateManager";
    private static final String CERTIFICATE_FILE = "server_cert.crt";
    private static final String CERTIFICATE_ALIAS = "antiy_server_cert";
    
    private static volatile CertificateManager instance;
    private X509TrustManager defaultTrustManager;
    private X509TrustManager customTrustManager;
    
    private CertificateManager() {
        initializeDefaultTrustManager();
    }
    
    public static CertificateManager getInstance() {
        if (instance == null) {
            synchronized (CertificateManager.class) {
                if (instance == null) {
                    instance = new CertificateManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化默认信任管理器
     */
    private void initializeDefaultTrustManager() {
        try {
            TrustManagerFactory factory = TrustManagerFactory.getInstance(
                TrustManagerFactory.getDefaultAlgorithm());
            factory.init((KeyStore) null);
            
            for (TrustManager tm : factory.getTrustManagers()) {
                if (tm instanceof X509TrustManager) {
                    defaultTrustManager = (X509TrustManager) tm;
                    break;
                }
            }
            
            Timber.i("默认信任管理器初始化完成");
            
        } catch (Exception e) {
            Timber.e(e, "初始化默认信任管理器失败");
        }
    }
    
    /**
     * 创建信任管理器
     * 根据配置的验证模式返回相应的信任管理器
     */
    public X509TrustManager createTrustManager(Context context) {
        if (customTrustManager != null) {
            return customTrustManager;
        }
        
        try {
            // 根据验证模式选择不同的信任管理器
            switch (Constants.Network.CERT_VALIDATION_MODE) {
                case Constants.Network.CERT_VALIDATION_STRICT:
                    customTrustManager = createStrictTrustManager(context);
                    Timber.i("使用严格证书验证模式");
                    break;
                case Constants.Network.CERT_VALIDATION_PERMISSIVE:
                    customTrustManager = createPermissiveTrustManager(context);
                    Timber.i("使用宽松证书验证模式");
                    break;
                case Constants.Network.CERT_VALIDATION_DISABLED:
                    customTrustManager = createDisabledTrustManager();
                    Timber.w("证书验证已禁用 - 仅用于开发环境");
                    break;
                default:
                    customTrustManager = createStrictTrustManager(context);
                    Timber.i("使用默认严格证书验证模式");
            }
            
            return customTrustManager;
            
        } catch (Exception e) {
            Timber.e(e, "创建自定义信任管理器失败，使用默认管理器");
            SecurityLogger.logSecurityEvent("TRUST_MANAGER_FALLBACK", e.getMessage());
            return defaultTrustManager;
        }
    }
    
    /**
     * 创建严格模式信任管理器
     * 只信任应用内置的证书和系统CA证书
     */
    private X509TrustManager createStrictTrustManager(Context context) throws Exception {
        // 尝试加载应用内置证书
        InputStream caInput = null;
        try {
            caInput = context.getAssets().open(CERTIFICATE_FILE);
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            Certificate ca = cf.generateCertificate(caInput);
            
            // 记录证书信息
            if (ca instanceof X509Certificate) {
                SecurityLogger.logCertificateInfo((X509Certificate) ca);
            }
            
            // 创建包含自定义证书的KeyStore
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null, null);
            keyStore.setCertificateEntry(CERTIFICATE_ALIAS, ca);
            
            // 创建信任管理器
            TrustManagerFactory tmf = TrustManagerFactory.getInstance(
                TrustManagerFactory.getDefaultAlgorithm());
            tmf.init(keyStore);
            
            Timber.i("严格模式信任管理器创建成功");
            return (X509TrustManager) tmf.getTrustManagers()[0];
            
        } catch (Exception e) {
            Timber.w(e, "无法加载自定义证书，使用系统默认证书");
            return defaultTrustManager;
        } finally {
            if (caInput != null) {
                try {
                    caInput.close();
                } catch (Exception e) {
                    Timber.e(e, "关闭证书输入流失败");
                }
            }
        }
    }
    
    /**
     * 创建宽松模式信任管理器
     * 首先尝试默认验证，失败时使用自定义验证逻辑
     */
    private X509TrustManager createPermissiveTrustManager(Context context) {
        return new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) 
                    throws CertificateException {
                // 客户端证书验证（通常不需要）
            }
            
            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) 
                    throws CertificateException {
                try {
                    // 首先尝试默认验证
                    if (defaultTrustManager != null) {
                        defaultTrustManager.checkServerTrusted(chain, authType);
                        Timber.d("默认证书验证通过");
                        return;
                    }
                } catch (CertificateException e) {
                    // 默认验证失败，尝试自定义证书验证
                    Timber.w("默认证书验证失败，尝试自定义验证: %s", e.getMessage());
                    validateCustomCertificate(chain, authType);
                }
            }
            
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return defaultTrustManager != null ? 
                    defaultTrustManager.getAcceptedIssuers() : new X509Certificate[0];
            }
            
            /**
             * 自定义证书验证逻辑
             */
            private void validateCustomCertificate(X509Certificate[] chain, String authType) 
                    throws CertificateException {
                if (chain == null || chain.length == 0) {
                    throw new CertificateException("证书链为空");
                }
                
                X509Certificate cert = chain[0];
                
                // 检查证书有效期
                try {
                    cert.checkValidity();
                    Timber.d("证书有效期检查通过");
                } catch (Exception e) {
                    throw new CertificateException("证书已过期或尚未生效", e);
                }
                
                // 记录证书信息
                SecurityLogger.logCertificateInfo(cert);
                
                // 可以添加更多自定义验证逻辑
                // 例如：检查证书指纹、验证特定字段等
                
                Timber.i("自定义证书验证通过");
            }
        };
    }
    
    /**
     * 创建禁用验证的信任管理器
     * 警告：仅用于开发环境，生产环境禁止使用
     */
    private X509TrustManager createDisabledTrustManager() {
        SecurityLogger.logSecurityEvent("CERT_VALIDATION_DISABLED", "证书验证已禁用");
        
        return new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
                // 不进行任何验证
            }
            
            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
                // 不进行任何验证
                if (chain != null && chain.length > 0) {
                    SecurityLogger.logCertificateInfo(chain[0]);
                }
            }
            
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        };
    }
    
    /**
     * 创建主机名验证器
     */
    public HostnameVerifier createHostnameVerifier() {
        return new HostnameVerifier() {
            @Override
            public boolean verify(String hostname, SSLSession session) {
                switch (Constants.Network.CERT_VALIDATION_MODE) {
                    case Constants.Network.CERT_VALIDATION_STRICT:
                        return verifyHostnameStrict(hostname, session);
                    case Constants.Network.CERT_VALIDATION_PERMISSIVE:
                        return verifyHostnamePermissive(hostname);
                    case Constants.Network.CERT_VALIDATION_DISABLED:
                        return true;
                    default:
                        return verifyHostnameStrict(hostname, session);
                }
            }
        };
    }
    
    /**
     * 严格主机名验证
     */
    private boolean verifyHostnameStrict(String hostname, SSLSession session) {
        try {
            // 使用默认的主机名验证器
            boolean result = HttpsURLConnection.getDefaultHostnameVerifier().verify(hostname, session);
            
            if (result) {
                Timber.d("严格主机名验证通过: %s", hostname);
                SecurityLogger.logSecurityEvent("HOSTNAME_VERIFIED", hostname);
            } else {
                Timber.w("严格主机名验证失败: %s", hostname);
                SecurityLogger.logSecurityEvent("HOSTNAME_VERIFICATION_FAILED", hostname);
            }
            
            return result;
            
        } catch (Exception e) {
            Timber.e(e, "严格主机名验证异常: %s", hostname);
            SecurityLogger.logSecurityEvent("HOSTNAME_VERIFICATION_ERROR", hostname + ": " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 宽松主机名验证
     */
    private boolean verifyHostnamePermissive(String hostname) {
        // 验证主机名是否在允许列表中
        String[] allowedHosts = getAllowedHosts();
        
        for (String allowedHost : allowedHosts) {
            if (hostname.equals(allowedHost) || isWildcardMatch(hostname, allowedHost)) {
                Timber.d("宽松主机名验证通过: %s", hostname);
                SecurityLogger.logSecurityEvent("HOSTNAME_ALLOWED", hostname);
                return true;
            }
        }
        
        Timber.w("主机名不在允许列表中: %s", hostname);
        SecurityLogger.logSecurityEvent("HOSTNAME_NOT_ALLOWED", hostname);
        return false;
    }
    
    /**
     * 获取允许的主机名列表
     */
    private String[] getAllowedHosts() {
        // 可以从配置文件或远程配置获取
        return new String[]{
            "************",     // 开发环境IP
            "*.antiy.com",      // 公司域名
            "*.antiy.cn",       // 公司域名
            "localhost",        // 本地测试
            "127.0.0.1",        // 本地回环
            "10.0.0.0/8",       // 内网IP段（需要特殊处理）
            "**********/12",    // 内网IP段
            "***********/16"    // 内网IP段
        };
    }
    
    /**
     * 通配符匹配
     */
    private boolean isWildcardMatch(String hostname, String pattern) {
        if (!pattern.startsWith("*.")) {
            return hostname.equals(pattern);
        }
        
        String domain = pattern.substring(2);
        return hostname.endsWith("." + domain) || hostname.equals(domain);
    }
    
    /**
     * 验证证书指纹（可选的额外安全措施）
     */
    public boolean validateCertificateFingerprint(X509Certificate cert, String expectedFingerprint) {
        try {
            // 计算证书SHA-256指纹
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-256");
            byte[] fingerprint = md.digest(cert.getEncoded());
            
            // 转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : fingerprint) {
                sb.append(String.format("%02x", b));
            }
            String actualFingerprint = sb.toString();
            
            boolean matches = actualFingerprint.equalsIgnoreCase(expectedFingerprint);
            
            if (matches) {
                Timber.i("证书指纹验证通过");
                SecurityLogger.logSecurityEvent("FINGERPRINT_VERIFIED", actualFingerprint);
            } else {
                Timber.w("证书指纹不匹配: 期望=%s, 实际=%s", expectedFingerprint, actualFingerprint);
                SecurityLogger.logSecurityEvent("FINGERPRINT_MISMATCH", 
                    "Expected: " + expectedFingerprint + ", Actual: " + actualFingerprint);
            }
            
            return matches;
            
        } catch (Exception e) {
            Timber.e(e, "计算证书指纹失败");
            SecurityLogger.logSecurityEvent("FINGERPRINT_ERROR", e.getMessage());
            return false;
        }
    }
}
