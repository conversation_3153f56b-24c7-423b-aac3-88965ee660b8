<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_header"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingLeft="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableLeft="@drawable/ic_logo_home"
            android:drawablePadding="6dp"
            android:text="@string/app_name"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/iv_setting"
            android:layout_width="48dp"
            android:layout_height="match_parent"
            android:scaleType="center"
            android:visibility="gone"
            android:src="@drawable/ic_setting" />
    </LinearLayout>

    <com.antiy.medr.view.ScoreView
        android:id="@+id/score_view"
        android:layout_width="330dp"
        android:layout_height="330dp"
        android:layout_below="@id/ll_header"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="48dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/score_view"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager" >

    </androidx.recyclerview.widget.RecyclerView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="0dp"
        android:layout_marginTop="0dp"
        android:layout_marginEnd="0dp"
        android:layout_marginBottom="1dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingLeft="16dp">

        <TextView
            android:id="@+id/version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:text="版本号:0.0.0.11"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>

</RelativeLayout>