package com.antiy.medr.device.controller;

import com.antiy.medr.auth.AuthApiManager;
import com.antiy.medr.data.source.Repository;
import com.antiy.medr.device.DevInterfaceManager;
import com.antiy.medr.model.EventEnum;
import com.antiy.medr.model.EventLog;
import com.blankj.utilcode.util.TimeUtils;
import com.blankj.utilcode.util.ToastUtils;

import java.util.List;

import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * Created on 2022/4/18.
 *
 * <AUTHOR>
 */
public class WipeDataController implements IController<String>{
    @Override
    public void enable() {
        AuthApiManager.getInstance().getApi().destroy();
    }

    @Override
    public void disable() {

    }

    @Override
    public boolean setWhiteList(List<String> whiteList) {
        return true;
    }
}
