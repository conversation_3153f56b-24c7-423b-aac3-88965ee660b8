package com.antiy.medr.ui;

import static com.antiy.medr.model.StatusEnum.ENABLE;
import static com.antiy.medr.model.StatusEnum.READ_WRITE;

import android.graphics.Color;
import android.os.Bundle;
import android.os.Environment;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.BackgroundColorSpan;
import android.text.style.ForegroundColorSpan;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;

import com.antiy.medr.R;
import com.antiy.medr.base.BaseActivity;
import com.antiy.medr.base.SpacesItemDecoration;
import com.antiy.medr.data.source.Repository;
import com.antiy.medr.databinding.ActivityTerminalWatchBinding;
import com.antiy.medr.device.controller.LockController;
import com.blankj.utilcode.util.SizeUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.rxjava.rxlife.RxLife;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.schedulers.Schedulers;
import timber.log.Timber;

/**
 * Created on 2022/4/2.
 *
 * <AUTHOR>
 */
public class TerminalWatchActivity extends BaseActivity<ActivityTerminalWatchBinding> {

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        final ActionBar supportActionBar = getSupportActionBar();
        if (supportActionBar != null) {
            supportActionBar.setDisplayHomeAsUpEnabled(true);
            supportActionBar.setHomeAsUpIndicator(R.drawable.ic_back);
        }
        final WatchAdapter adapter = new WatchAdapter(new ArrayList<>());
        binding.recyclerView.setAdapter(adapter);
        final int padding = SizeUtils.dp2px(16);
        binding.recyclerView.addItemDecoration(new SpacesItemDecoration(padding, padding, padding, padding));
        Repository.getInstance().getConfig()
                .subscribeOn(Schedulers.io())
                .to(RxLife.toMain(this))
                .subscribe(configModel -> {
                    Timber.i(configModel.toString());
                    final ArrayList<String> data = new ArrayList<>();
                    String desc = "未授权";
                    if (configModel.getUsbAuth() != null) {
                        switch (configModel.getUsbAuth()) {
                            case ENABLE:
                                desc = "只读";
                                break;
                            case READ_WRITE:
                                desc = "可读可写";
                                break;
                            default:
                                break;
                        }
                    }
                    String sddesc = "未授权";
                    if (configModel.getSdAuth() != null) {
                        switch (configModel.getSdAuth()) {
                            case ENABLE:
                                sddesc = "只读";
                                break;
                            case READ_WRITE:
                                sddesc = "可读可写";
                                break;
                            default:
                                break;
                        }
                    }
                    data.add("USB设备管控：   " + desc);
                    data.add("SD卡读写控制：   " + sddesc);
                    data.add("蓝牙使能控制：   " +  (configModel.getBluetoothAuth() == null  || configModel.getBluetoothAuth() != ENABLE ? "未授权":"授权"));
                    data.add("违规外联管控状态："+"管控中");
                    //data.add("网络外联管控："+(configModel.getIpWl().size()==0 ? "未管控":"已管控"));
                    //data.add("终端锁定状态："+(LockController.isLock() ? "已锁定":"未锁定"));
                    adapter.setList(data);
                });
    }

    private static class WatchAdapter extends BaseQuickAdapter<String, BaseViewHolder> {
        public WatchAdapter(List<String> data) {
            super(R.layout.item_watch_list, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder baseViewHolder, String desc) {
            SpannableString spannableString =new SpannableString(desc);

            ForegroundColorSpan red = new ForegroundColorSpan(Color.RED);
            ForegroundColorSpan green = new ForegroundColorSpan(Color.GREEN);

            if(desc.contains("未")){
                spannableString.setSpan(red,desc.indexOf("：")+1,desc.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
            }else {
                spannableString.setSpan(green,desc.indexOf("：")+1,desc.length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
            }

            baseViewHolder.setText(R.id.tv_name, spannableString);
        }
    }
}