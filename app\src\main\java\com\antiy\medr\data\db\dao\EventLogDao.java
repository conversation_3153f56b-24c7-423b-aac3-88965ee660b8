package com.antiy.medr.data.db.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.antiy.medr.model.EventLog;

import java.util.List;

import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Single;

/**
 * 违规时间dao
 * Created on 2022/4/15.
 *
 * <AUTHOR>
 */
@Dao
public interface EventLogDao {

    /**
     * 插入多条事件日志
     * @param log
     * @return
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    Completable insertLogs(EventLog... log);

    /**
     * 查询所有事件日志
     * @return
     */
    @Query("SELECT * FROM event_log")
    Single<List<EventLog>> queryAll();

    /**
     * 删除指定日志
     * @param eventLogs
     * @return
     */
    @Delete
    Completable delete(EventLog... eventLogs);

    /**
     * 删除所有日志
     * @return
     */
    @Query("DELETE FROM event_log")
    Completable deleteAll();
}
