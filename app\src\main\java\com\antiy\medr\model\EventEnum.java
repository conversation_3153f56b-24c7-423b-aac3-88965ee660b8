package com.antiy.medr.model;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.google.gson.annotations.JsonAdapter;

import java.lang.reflect.Type;

/**
 * Created on 2022/4/14.
 * 事件类型
 *
 * <AUTHOR>
 */
@JsonAdapter(EventEnumAdapter.class)
public enum EventEnum {
    UNKNOWN(""),
    BLUETOOTH("bluetooth"),
    SDCARD("sd"),
    USB("usb"),
    WIFI("wifi"),
    LOCK("lock"),
    WIPE_DATA("wipe_data");

    private String name;

    EventEnum(String name) {
        this.name = name;
    }

    public static EventEnum get(String name) {
        for (EventEnum value : EventEnum.values()) {
            if (value.name.equals(name)) {
                return value;
            }
        }
        return UNKNOWN;
    }

    public String getName() {
        return name;
    }
}

class EventEnumAdapter implements JsonSerializer<EventEnum>, JsonDeserializer<EventEnum> {

    @Override
    public EventEnum deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        return EventEnum.get(json.getAsString());
    }

    @Override
    public JsonElement serialize(EventEnum src, Type typeOfSrc, JsonSerializationContext context) {
        return new JsonPrimitive(src.getName());
    }
}
