<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.antiy.medr"
    android:versionCode="2"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="23" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="ndroid.permission.MANAGE_DEVICE_ADMINS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.DEVICE_POWER" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.USES_POLICY_FORCE_LOCK" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />

    <permission
        android:name="com.antiy.medr.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.antiy.medr.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.antiy.medr.app.App"
        android:allowBackup="false"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:directBootAware="true"
        android:extractNativeLibs="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Medr" >
        <service
            android:name="com.antiy.medr.service.KeepAliveService"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="com.antiy.medr.service.KeepAliveService" />
            </intent-filter>
        </service>
        <service
            android:name="com.antiy.medr.device.LockScreenService"
            android:enabled="true"
            android:exported="true" />

        <receiver
            android:name="com.antiy.medr.auth.AuthReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.SCREEN_ON" />
                <action android:name="com.cetc30.auth.logout" />
                <action android:name="com.cetc30.auth.login" />
            </intent-filter>
        </receiver>

        <activity
            android:name="com.antiy.medr.ui.SplashActivity"
            android:exported="true" />
        <activity
            android:name="com.antiy.medr.ui.TerminalWatchActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="false"
            android:label="@string/terminal_security_control"
            android:screenOrientation="fullSensor" />
        <activity
            android:name="com.antiy.medr.ui.MainActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="fullSensor" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!--
            <service
            android:name=".AccessService"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            >

            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/service_config"/>
        </service>
        -->

        <receiver
            android:name="com.antiy.medr.device.AdminReceiver"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:label="@string/app_name"
            android:permission="android.permission.BIND_DEVICE_ADMIN" >
            <meta-data
                android:name="android.app.device_admin"
                android:resource="@xml/device_admin" />

            <intent-filter>
                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
            </intent-filter>
        </receiver> <!-- <activity -->
        <!-- android:name="com.cetc30.auth.activity.SplashActivity" -->
        <!-- android:exported="true" -->
        <!-- android:taskAffinity="com.cetc30.auth" -->
        <!-- tools:ignore="MissingClass" /> -->
        <!-- <activity -->
        <!-- android:name="com.cetc30.auth.activity.LoginActivity" -->
        <!-- android:exported="true" -->
        <!-- android:taskAffinity="com.cetc30.auth" -->
        <!-- tools:ignore="MissingClass" /> &lt;!&ndash; <activity &ndash;&gt; -->
        <!-- android:name="com.cetc30.auth.activity.ConfirmLoginActivity" -->
        <!-- android:exported="true" -->
        <!-- android:taskAffinity="com.cetc30.auth" -->
        <!-- tools:ignore="MissingClass" /> -->
        <activity
            android:name="com.cetc30.auth.sdk.api.AgentActivity"
            android:exported="true" />

        <service android:name="org.xplugin.core.app.ServicesProxy" />

        <provider
            android:name="org.xplugin.core.app.ContentProviderProxy"
            android:authorities="com.antiy.medr.xPlugin.Provider"
            android:exported="true"
            android:grantUriPermissions="true" />

        <activity
            android:name="org.xplugin.tpl.DefaultActivity"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="standard" />
        <activity
            android:name="org.xplugin.tpl.DefaultActivity_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="standard"
            android:theme="@android:style/Theme.Translucent" /> <!-- singleTop Activities -->
        <activity
            android:name="org.xplugin.tpl.SingleTopActivity_1"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTop" />
        <activity
            android:name="org.xplugin.tpl.SingleTopActivity_2"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTop" />
        <activity
            android:name="org.xplugin.tpl.SingleTopActivity_3"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTop" />
        <activity
            android:name="org.xplugin.tpl.SingleTopActivity_4"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTop" />
        <activity
            android:name="org.xplugin.tpl.SingleTopActivity_5"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTop" />
        <activity
            android:name="org.xplugin.tpl.SingleTopActivity_1_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="org.xplugin.tpl.SingleTopActivity_2_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="org.xplugin.tpl.SingleTopActivity_3_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="org.xplugin.tpl.SingleTopActivity_4_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="org.xplugin.tpl.SingleTopActivity_5_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent" /> <!-- singleTask Activities -->
        <activity
            android:name="org.xplugin.tpl.SingleTaskActivity_1"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTask" />
        <activity
            android:name="org.xplugin.tpl.SingleTaskActivity_2"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTask" />
        <activity
            android:name="org.xplugin.tpl.SingleTaskActivity_3"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTask" />
        <activity
            android:name="org.xplugin.tpl.SingleTaskActivity_4"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTask" />
        <activity
            android:name="org.xplugin.tpl.SingleTaskActivity_5"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTask" />
        <activity
            android:name="org.xplugin.tpl.SingleTaskActivity_1_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="org.xplugin.tpl.SingleTaskActivity_2_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="org.xplugin.tpl.SingleTaskActivity_3_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="org.xplugin.tpl.SingleTaskActivity_4_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="org.xplugin.tpl.SingleTaskActivity_5_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent" /> <!-- singleInstance Activities -->
        <activity
            android:name="org.xplugin.tpl.SingleInstanceActivity_1"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleInstance" />
        <activity
            android:name="org.xplugin.tpl.SingleInstanceActivity_2"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleInstance" />
        <activity
            android:name="org.xplugin.tpl.SingleInstanceActivity_3"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleInstance" />
        <activity
            android:name="org.xplugin.tpl.SingleInstanceActivity_4"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleInstance" />
        <activity
            android:name="org.xplugin.tpl.SingleInstanceActivity_5"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleInstance" />
        <activity
            android:name="org.xplugin.tpl.SingleInstanceActivity_1_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleInstance"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="org.xplugin.tpl.SingleInstanceActivity_2_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleInstance"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="org.xplugin.tpl.SingleInstanceActivity_3_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleInstance"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="org.xplugin.tpl.SingleInstanceActivity_4_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleInstance"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="org.xplugin.tpl.SingleInstanceActivity_5_t"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="false"
            android:launchMode="singleInstance"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/ActivityTranslucent"
            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
        <activity
            android:name="com.blankj.utilcode.util.UtilsTransActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:multiprocess="true"
            android:theme="@style/ActivityTranslucent"
            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />

        <provider
            android:name="com.blankj.utilcode.util.UtilsFileProvider"
            android:authorities="com.antiy.medr.utilcode.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/util_code_provider_paths" />
        </provider>

        <service
            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.antiy.medr.messenger" />
            </intent-filter>
        </service>
        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.antiy.medr.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>