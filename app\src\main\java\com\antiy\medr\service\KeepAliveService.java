package com.antiy.medr.service;

import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.provider.Settings;
import android.util.Log;

import com.antiy.medr.device.DevInterfaceManager;
import com.antiy.medr.ui.MainActivity;
import com.blankj.utilcode.util.ToastUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Consumer;

/**
 * 后台保活服务
 * Created on 2022/8/5.
 *
 * <AUTHOR>
 */
public class KeepAliveService extends Service {

    @Override
    public void onCreate() {
        super.onCreate();

        Observable.interval(10, TimeUnit.SECONDS)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> {
                    setScreenOffTime(10*1000*60);

                });

        Intent intent1 =new Intent(this, MainActivity.class);
        intent1.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        this.startActivity(intent1);
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private void setScreenOffTime(int paramInt) {
        try {
            Settings.System.putInt(getContentResolver(), Settings.System.SCREEN_OFF_TIMEOUT,
                    paramInt);

        } catch (Exception localException) {
            localException.printStackTrace();

        }

    }

}