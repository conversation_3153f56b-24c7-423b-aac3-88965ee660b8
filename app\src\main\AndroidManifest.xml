<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.antiy.medr">

    <uses-sdk
        android:minSdkVersion="22"
        tools:overrideLibrary="com.cetc30.auth.sdk" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="ndroid.permission.MANAGE_DEVICE_ADMINS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.DEVICE_POWER"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.USES_POLICY_FORCE_LOCK" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE"
        tools:ignore="ProtectedPermissions" />


    <application
        android:name=".app.App"
        android:allowBackup="false"
        android:directBootAware="true"
        android:extractNativeLibs="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Medr">
        <service
            android:name=".service.KeepAliveService"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="com.antiy.medr.service.KeepAliveService" />
            </intent-filter>
        </service>
        <service
            android:name=".device.LockScreenService"
            android:enabled="true"
            android:exported="true" />

        <receiver
            android:name=".auth.AuthReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.SCREEN_ON" />
                <action android:name="com.cetc30.auth.logout" />
                <action android:name="com.cetc30.auth.login" />
            </intent-filter>
        </receiver>

        <activity
            android:name=".ui.SplashActivity"
            android:exported="true" />
        <activity
            android:name=".ui.TerminalWatchActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="false"
            android:label="@string/terminal_security_control"
            android:screenOrientation="fullSensor" />
        <activity
            android:name=".ui.MainActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="fullSensor">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

<!--        <service
            android:name=".AccessService"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
            >

            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/service_config"/>
        </service>-->

        <receiver
            android:name=".device.AdminReceiver"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:label="@string/app_name"
            android:permission="android.permission.BIND_DEVICE_ADMIN">
            <meta-data
                android:name="android.app.device_admin"
                android:resource="@xml/device_admin" />

            <intent-filter>
                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
            </intent-filter>
        </receiver> <!-- <activity -->
        <!-- android:name="com.cetc30.auth.activity.SplashActivity" -->
        <!-- android:exported="true" -->
        <!-- android:taskAffinity="com.cetc30.auth" -->
        <!-- tools:ignore="MissingClass" /> -->
        <!-- <activity -->
        <!-- android:name="com.cetc30.auth.activity.LoginActivity" -->
        <!-- android:exported="true" -->
        <!-- android:taskAffinity="com.cetc30.auth" -->
        <!-- tools:ignore="MissingClass" /> &lt;!&ndash; <activity &ndash;&gt; -->
        <!-- android:name="com.cetc30.auth.activity.ConfirmLoginActivity" -->
        <!-- android:exported="true" -->
        <!-- android:taskAffinity="com.cetc30.auth" -->
        <!-- tools:ignore="MissingClass" /> -->
        <activity
            android:name="com.cetc30.auth.sdk.api.AgentActivity"
            android:exported="true" />

    </application>

</manifest>