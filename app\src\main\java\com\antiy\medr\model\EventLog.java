package com.antiy.medr.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.google.gson.annotations.SerializedName;

/**
 * Created on 2022/4/14.
 *
 * <AUTHOR>
 */
@Entity(tableName = "event_log")
public class EventLog {
    @PrimaryKey(autoGenerate = true)
    private int id;
    @SerializedName("type")
    private EventEnum event;
    private String desc;
    public String name;
    @SerializedName("create_at")
    private String time;

    public EventLog(EventEnum event,String name, String desc, String time) {
        this.event = event;
        this.desc = desc;
        this.time = time;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public EventEnum getEvent() {
        return event;
    }

    public void setEvent(EventEnum event) {
        this.event = event;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }
}
