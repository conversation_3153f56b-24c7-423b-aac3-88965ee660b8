package com.antiy.medr;

import android.Manifest;
import android.content.Context;
import android.util.Log;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.GrantPermissionRule;

import com.android.DevCtrl.DevInterface;
import com.blankj.utilcode.constant.MemoryConstants;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;

import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Instrumented test, which will execute on an Android device.
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
@RunWith(AndroidJUnit4.class)
public class ExampleInstrumentedTest {

    @Rule
    public GrantPermissionRule mRuntimePermissionRule = GrantPermissionRule.grant(
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.ACCESS_WIFI_STATE,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.INTERNET);

    @Test
    public void useAppContext() {
        // Context of the app under test.
        Context appContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        final DevInterface devInterface = new DevInterface(appContext);
        devInterface.setWlanWhiteList(2, null);
    }

    @Test
    public void wifiTest() {
        // Context of the app under test.
        Context appContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        final ArrayList<String> lowercaseList = new ArrayList<>();
        final JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("Mac", "4c:50:77:df:1f:2a");
        lowercaseList.add("4c:50:77:df:1f:2a");
        final String[] ans = lowercaseList.toArray(new String[0]);
        new DevInterface(appContext).setWlanWhiteList(1, ans);
    }

    @Test
    public void usbTest() {
        Context appContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        final boolean b = new DevInterface(appContext).setPeripheralPolicies(1);
        ToastUtils.showShort(String.valueOf(b));
    }

    @Test
    public void sdTest() {
        Context appContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        final boolean b = new DevInterface(appContext).setExternalStoragePolicies(2);
        ToastUtils.showShort(String.valueOf(b));
    }

    @Test
    public void getWifiBssid() {
        final ArrayList<String> ans = new ArrayList<>();
        final ArrayList<String> whiteList = new ArrayList<>();
        whiteList.add("hehe");
        whiteList.add("hehe");
        whiteList.add("hehe");
        for (String mac : whiteList) {
            final JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("Mac", mac);
            final String jsonStr = jsonObject.toString();
            ans.add(jsonStr);
        }
        System.out.println(ans);
    }

    @Test
    public void bluetooth() {
        Context appContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        final boolean b = new DevInterface(appContext).setBluetoothPolicies(2,null);
        final JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("Mac","B0:33:66:D6:AE:C3");
        new DevInterface(appContext)
                .setBluetoothPolicies(1, new String[]{
                        jsonObject.toString()
                });
    }

    /**
     * 创建固定大小的文件
     *
     * @param file
     * @throws IOException
     */
    public void createFixLengthFile(File file) throws IOException {
        long start = System.currentTimeMillis();
        byte a = 97;
        for (int i = 0; i < 1024; i++) {
            final byte[] b = new byte[MemoryConstants.MB];
            Arrays.fill(b, a);
            try (final RandomAccessFile randomAccessFile = new RandomAccessFile(file, "rw");
                 final FileChannel output = randomAccessFile.getChannel()) {
                randomAccessFile.seek(randomAccessFile.length());
                final ByteBuffer buffer = ByteBuffer.wrap(b);
                int len = 0;
                while ((len = output.write(buffer)) != 0) {

                }
            }
        }
        System.out.println("写入" + file.getAbsolutePath() + " 大小: " + FileUtils.getSize(file) + " 时间:" + (System.currentTimeMillis() - start) + "ms");
    }

    @Test
    public void dqTest() {
        final ArrayList<String> whiteList = new ArrayList<>();
        whiteList.add("93258274/104362018");
        whiteList.add("25479/1423");
        whiteList.add("407664/22768");
        whiteList.add("0/0");
   /*     final boolean success = new UsbController()
                .setWhiteList(whiteList);
        System.out.println("white list:"+success);*/
    }
    @Test
    public void wipe() {
        Context appContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        new DevInterface(appContext).wipeDeviceData();
    }

    @Test
    public void sdcard() {
        Context appContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        new DevInterface(appContext).setExternalStoragePolicies(2);
    }

    @Test
    public void checkSdCard() {
        Context appContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        final int externalStoragePolicies = new DevInterface(appContext).getExternalStoragePolicies();
        System.out.println(externalStoragePolicies);
        new DevInterface(appContext).setExternalStoragePolicies(2);
    }
    @Test
    public void checkJson() {
        JsonObject jsonObject = GsonUtils.fromJson("{\"bluetooth_auth\":1}}",JsonObject.class);
        JsonObject jsonObject1 = GsonUtils.fromJson("{\"code\":0,\"msg\":\"\",\"cmd\":\"ws_initial\",\"data\":{\"usb_wl\":[\"1423/25479\",\"25479/1423\",\"5383/6243\",\"6243/5383\",\"1507/1863\",\"1863/1507\",\"8738/4369\"],\"wifi_wl\":null,\"ip_wl\":[{\"ip_name\":\"tp测试5\",\"protocol\":\"tcp\",\"ip_type\":\"inbound\",\"src_type\":\"single\",\"src_ip\":\"************\",\"src_ip_start\":\"\",\"src_ip_end\":\"\",\"src_port\":\"8100\",\"dest_type\":\"\",\"dest_ip\":\"\",\"dest_ip_start\":\"\",\"dest_ip_end\":\"\",\"dest_port\":\"\"},{\"ip_name\":\"tp测试5\",\"protocol\":\"tcp\",\"ip_type\":\"inbound\",\"src_type\":\"single\",\"src_ip\":\"************\",\"src_ip_start\":\"\",\"src_ip_end\":\"\",\"src_port\":\"8100\",\"dest_type\":\"\",\"dest_ip\":\"\",\"dest_ip_start\":\"\",\"dest_ip_end\":\"\",\"dest_port\":\"\"},{\"ip_name\":\"adb\",\"protocol\":\"tcp\",\"ip_type\":\"inbound\",\"src_type\":\"multiple\",\"src_ip\":\"\",\"src_ip_start\":\"************\",\"src_ip_end\":\"**************\",\"src_port\":\"\",\"dest_type\":\"single\",\"dest_ip\":\"\",\"dest_ip_start\":\"\",\"dest_ip_end\":\"\",\"dest_port\":\"5555\"},{\"ip_name\":\"sfb\",\"protocol\":\"icmp\",\"ip_type\":\"outbound\",\"src_type\":\"single\",\"src_ip\":\"\",\"src_ip_start\":\"\",\"src_ip_end\":\"\",\"src_port\":\"\",\"dest_type\":\"multiple\",\"dest_ip\":\"\",\"dest_ip_start\":\"************\",\"dest_ip_end\":\"**************\",\"dest_port\":\"\"},{\"ip_name\":\"sdfsef\",\"protocol\":\"icmp\",\"ip_type\":\"inbound\",\"src_type\":\"multiple\",\"src_ip\":\"\",\"src_ip_start\":\"************\",\"src_ip_end\":\"**************\",\"src_port\":\"\",\"dest_type\":\"single\",\"dest_ip\":\"\",\"dest_ip_start\":\"\",\"dest_ip_end\":\"\",\"dest_port\":\"\"}],\"bluetooth\":[\"00:94:EC:FC:83:59\",\"\",\"\"]}}",JsonObject.class);

        try {

            if (jsonObject.get("usb_wl")!=null && !jsonObject.get("usb_wl").isJsonArray()) {
                jsonObject.add("usb_wl",null);
            }
            if (jsonObject.get("wifi_wl")!=null && !jsonObject.get("wifi_wl").isJsonArray()) {
                jsonObject.add("wifi_wl",null);
            }
            if (jsonObject.get("ip_wl")!=null && !jsonObject.get("ip_wl").isJsonArray()) {
                jsonObject.add("ip_wl",null);
            }
            if (jsonObject.get("bluetooth")!=null && !jsonObject.get("bluetooth").isJsonArray()) {
                jsonObject.add("bluetooth",null);
            }

        }catch (Exception e){
            e.printStackTrace();
        }


        Context appContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        final int externalStoragePolicies = new DevInterface(appContext).getExternalStoragePolicies();
        System.out.println(externalStoragePolicies);
        new DevInterface(appContext).setExternalStoragePolicies(2);
    }
}
