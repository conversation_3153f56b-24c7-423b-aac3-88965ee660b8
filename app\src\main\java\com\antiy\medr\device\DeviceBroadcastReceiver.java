package com.antiy.medr.device;

import android.bluetooth.BluetoothAdapter;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiManager;
import android.os.Bundle;

import com.antiy.medr.auth.AuthApiManager;
import com.antiy.medr.data.source.Repository;
import com.antiy.medr.device.controller.BluetoothController;
import com.antiy.medr.device.controller.IptablesController;
import com.antiy.medr.device.controller.LockController;
import com.antiy.medr.device.controller.SdCardController;
import com.antiy.medr.device.controller.UsbController;
import com.antiy.medr.device.controller.WifiController;
import com.antiy.medr.device.controller.WipeDataController;
import com.antiy.medr.model.ConfigModel;
import com.antiy.medr.model.EventEnum;
import com.antiy.medr.model.EventLog;
import com.antiy.medr.model.IptablesModel;
import com.antiy.medr.network.Api;
import com.antiy.medr.network.WebSocketManager;
import com.antiy.medr.util.CrashHandler;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.TimeUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.cetc30.auth.sdk.utils.ServerUtils;
import com.google.gson.JsonElement;

import org.xutils.common.util.IOUtil;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.core.Maybe;
import io.reactivex.rxjava3.schedulers.Schedulers;
import timber.log.Timber;

/**
 * 系统设备广播监听
 * Created on 2022/4/14.
 *
 * <AUTHOR>
 */
public class DeviceBroadcastReceiver extends BroadcastReceiver {

    /**
     * 广播注册
     *
     * @param context
     */
    public static void register(Context context) {
        final IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED);
        intentFilter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED);
        intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
        intentFilter.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION);
        intentFilter.addAction(Intent.ACTION_SCREEN_OFF);
        intentFilter.addAction(Intent.ACTION_SCREEN_ON);
        intentFilter.addAction(Intent.ACTION_MEDIA_MOUNTED);
        intentFilter.addAction(Intent.ACTION_MEDIA_UNMOUNTED);
        intentFilter.addAction("com.cetc30.auth.config");
        intentFilter.addAction("com.tdtech.disallow.sd.device");
        intentFilter.addAction("com.tdtech.disallow.usb.device");
        intentFilter.addAction("com.tdtech.disallow.bluetooth");
        intentFilter.addAction("com.tdtech.disallow.netpkg");
        context.registerReceiver(new DeviceBroadcastReceiver(), intentFilter);
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        Timber.i(intent.getAction());
        switch (intent.getAction()) {
            case UsbManager.ACTION_USB_DEVICE_ATTACHED:
                UsbDevice device = (UsbDevice) intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);

                String pid = "0000".substring(0,4-Integer.toString(device.getProductId(),16).length())+Integer.toString(device.getProductId(),16);
                String vid = "0000".substring(0,4-Integer.toString(device.getVendorId(),16).length())+Integer.toString(device.getVendorId(),16);

                Repository.getInstance()
                        .eventLog(new EventLog(EventEnum.USB, "接入外部存储","接入外部存储，设备pid为"+pid+"、vid为"+vid, TimeUtils.getNowString()))
                        .subscribeOn(Schedulers.io())
                        .subscribe();
                break;
            case UsbManager.ACTION_USB_DEVICE_DETACHED:
                UsbDevice device1 = (UsbDevice) intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);

                String pid1 = "0000".substring(0,4-Integer.toString(device1.getProductId(),16).length())+Integer.toString(device1.getProductId(),16);
                String vid1 = "0000".substring(0,4-Integer.toString(device1.getVendorId(),16).length())+Integer.toString(device1.getVendorId(),16);

                Repository.getInstance()
                        .eventLog(new EventLog(EventEnum.USB, "移除外部存储","移除外部存储，设备pid为"+pid1+"、vid为"+vid1, TimeUtils.getNowString()))
                        .subscribeOn(Schedulers.io())
                        .subscribe();
                break;
            case WifiManager.WIFI_STATE_CHANGED_ACTION:
                handleWifiState(intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, 0));
                break;
            case BluetoothAdapter.ACTION_STATE_CHANGED:
                handleBluetoothState(intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR));
                break;
            case Intent.ACTION_SCREEN_OFF:
                if (!LockController.isLock()) {
                    AuthApiManager.getInstance().getApi().screenLock();
                }
                break;
            case Intent.ACTION_SCREEN_ON:
                break;
            case Intent.ACTION_MEDIA_MOUNTED:
                Repository.getInstance()
                        .eventLog(new EventLog(EventEnum.SDCARD, "挂载储存卡","挂载存储卡", TimeUtils.getNowString()))
                        .subscribeOn(Schedulers.io())
                        .subscribe();
                break;
            case Intent.ACTION_MEDIA_UNMOUNTED:
                Repository.getInstance()
                        .eventLog(new EventLog(EventEnum.SDCARD, "卸载存储卡","卸载存储卡", TimeUtils.getNowString()))
                        .subscribeOn(Schedulers.io())
                        .subscribe();
                break;
            case "com.cetc30.auth.config":
                String serverip = ServerUtils.getServerIp();
                if (serverip!=null) {
                    Repository.getInstance().saveWsIp(serverip);
                    WebSocketManager.getInstance().connect(Api.getWsLinkFromIp(serverip));

                    Maybe.fromObservable(Repository.getInstance()
                                    .getConfig())
                            .subscribe(configModel -> {
                                //从缓存中重新更新iptables
                                new IptablesController().setWhiteList(configModel.getIpWl());
                            });
                }

                break;
            case "com.tdtech.disallow.sd.device":
                Bundle sddeviceinfo1 = intent.getBundleExtra("deviceinfo");
                if (sddeviceinfo1!=null) {
                    Repository.getInstance()
                            .eventLog(new EventLog(EventEnum.SDCARD, "违规外联","发现违规存储卡,名称:"+sddeviceinfo1.getString("name","")+",设备标识:"+sddeviceinfo1.getString("pv",""), TimeUtils.getNowString()))
                            .subscribeOn(Schedulers.io())
                            .subscribe();
                }
                /*
               if (lastsdtime == 0 || System.currentTimeMillis()-lastsdtime>10000){ //设备开机会反复发现违规存储卡，控制10s过滤
                    Repository.getInstance()
                            .eventLog(new EventLog(EventEnum.SDCARD, "违规外联","发现违规存储卡", TimeUtils.getNowString()))
                            .subscribeOn(Schedulers.io())
                            .subscribe();
                    lastsdtime = System.currentTimeMillis();

                }*/

                break;
            case "com.tdtech.disallow.usb.device":
                Bundle usbdeviceinfo = intent.getBundleExtra("deviceinfo");

                if (usbdeviceinfo!=null) {
                    String[] pv = usbdeviceinfo.getString("pv","").split("/");
                    String pvresult="";
                    if (pv.length==3) {
                        try {
                            StringBuilder sb = new StringBuilder();
                            sb.append("0000".substring(0, 4 - Integer.toString(Integer.parseInt(pv[0]), 16).length())).append(Integer.toString(Integer.parseInt(pv[0]), 16));
                            sb.append("/");
                            sb.append("0000".substring(0, 4 - Integer.toString(Integer.parseInt(pv[1]), 16).length())).append(Integer.toString(Integer.parseInt(pv[1]), 16));
                            sb.append("/");
                            sb.append(pv[2]);
                            pvresult = sb.toString();
                        }catch (Exception e){
                            e.printStackTrace();
                        }

                    }

                    CrashHandler.addlog("usbdeviceinfo:"+usbdeviceinfo!=null?pvresult:"null");
                    Repository.getInstance()
                            .eventLog(new EventLog(EventEnum.USB, "违规外联","发现违规usb,名称:"+usbdeviceinfo.getString("name","")+",pid/vid:"+pvresult, TimeUtils.getNowString()))
                            .subscribeOn(Schedulers.io())
                            .subscribe();
                }

                break;
            case "com.tdtech.disallow.bluetooth":
                Bundle bluetoothdeviceinfo2 = intent.getBundleExtra("deviceinfo");
                if (bluetoothdeviceinfo2!=null) {
                    Repository.getInstance()
                            .eventLog(new EventLog(EventEnum.BLUETOOTH, "违规外联","发现违规蓝牙,名称/地址:"+bluetoothdeviceinfo2.getString("name",""), TimeUtils.getNowString()))
                            .subscribeOn(Schedulers.io())
                            .subscribe();
                }

                break;
            case "com.tdtech.disallow.netpkg":
                try {
                    Bundle netpkg = intent.getBundleExtra("netpkg");

                    if (netpkg!=null) {

                        String ip = NetworkUtils.getIPAddress(true);
                        if (netpkg.getString("sip","").trim().equals(ip.trim()) ) {

                            if (!bcFilter(netpkg.getString("sip","").trim(),0)) {
                                Repository.getInstance()
                                        .eventLog(new EventLog(EventEnum.BLUETOOTH, "违规外联","发现违规网络外联,违规访问ip:"+netpkg.getString("dip",""), TimeUtils.getNowString()))
                                        .subscribeOn(Schedulers.io())
                                        .subscribe();
                            }


                        }
                        if (netpkg.getString("dip","").trim().equals(ip.trim()) ) {

                            if (!bcFilter(netpkg.getString("sip","").trim(),1)) {
                                Repository.getInstance()
                                        .eventLog(new EventLog(EventEnum.BLUETOOTH, "违规外联","发现违规网络外联,被ip:"+netpkg.getString("sip","")+"违规访问", TimeUtils.getNowString()))
                                        .subscribeOn(Schedulers.io())
                                        .subscribe();
                            }

                        }
                    }

                }catch (Exception e){
                    CrashHandler.addlog(e.getMessage());
                }

                break;
            default:
                break;
        }

    }

    /**
     * 处理wifi开启相关事件
     * @param state
     */
    private void handleWifiState(int state) {
        switch (state) {
            case WifiManager.WIFI_STATE_DISABLED:
                Timber.d("wifi关闭");
                break;
            case WifiManager.WIFI_STATE_ENABLED:
                Timber.d("wifi开启");
                break;
            default:
                break;
        }
    }

    /**
     * 处理蓝牙开启关闭相关事件
     *
     * @param state
     */
    private void handleBluetoothState(int state) {
        switch (state) {
            case BluetoothAdapter.STATE_OFF:
                Timber.d("蓝牙关闭");
                Repository.getInstance()
                        .eventLog(new EventLog(EventEnum.BLUETOOTH, "关闭蓝牙","关闭蓝牙", TimeUtils.getNowString()))
                        .subscribeOn(Schedulers.io())
                        .subscribe();
                break;
            case BluetoothAdapter.STATE_ON:
                Timber.d("蓝牙开启");
                Repository.getInstance()
                        .eventLog(new EventLog(EventEnum.BLUETOOTH, "开启蓝牙","开启蓝牙", TimeUtils.getNowString()))
                        .subscribeOn(Schedulers.io())
                        .subscribe();
                break;
            default:
                break;
        }
    }
   /**
    * iptables -F 期间会有日志产生，需要过滤掉 ,0: 访问 ，1: 被访问
    * */
   private boolean bcFilter(String ip,int type){

       return IptablesController.is_operate;
 /*      ConfigModel configModel=Repository.getInstance().getConfig().blockingFirst();
       List<IptablesModel> ipWl = configModel.getIpWl();
       if (type==0) {
           for (IptablesModel iptablesModel : ipWl) {
               if (iptablesModel.getIp_type().trim().equals("outbound") &&
                       (iptablesModel.getDest_ip().trim().equals(ip) ||
                               (iptablesModel.getDest_type().trim().equals("multiple") && iptablesModel.getDest_ip_start().trim().compareTo(ip)<0 && iptablesModel.getDest_ip_end().trim().compareTo(ip)>0) ) ){
                   return true;
               }
           }
       }else if(type==1){
           for (IptablesModel iptablesModel : ipWl) {
               if (iptablesModel.getIp_type().trim().equals("inbound") &&
                       (iptablesModel.getSrc_ip().trim().equals(ip) ||
                               (iptablesModel.getSrc_type().trim().equals("multiple") && iptablesModel.getSrc_ip_start().trim().compareTo(ip) <0 && iptablesModel.getSrc_ip_end().trim().compareTo(ip)>0  ))) {
                   return true;
               }
           }
       }
       return false;*/
   }

}