1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.antiy.medr"
4    android:versionCode="2"
5    android:versionName="1.0" >
6
7    <uses-sdk
7-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:6:5-8:55
8        android:minSdkVersion="23"
8-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:7:9-35
9        android:targetSdkVersion="23" />
9-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:6:5-8:55
10
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:10:5-79
11-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:10:22-76
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:11:5-67
12-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:11:22-64
13    <uses-permission android:name="android.permission.REORDER_TASKS" />
13-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:12:5-72
13-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:12:22-69
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:13:5-81
14-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:13:22-78
15    <uses-permission android:name="ndroid.permission.MANAGE_DEVICE_ADMINS" />
15-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:14:5-78
15-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:14:22-75
16    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
16-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:15:5-80
16-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:15:22-77
17    <uses-permission android:name="android.permission.DEVICE_POWER" />
17-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:16:5-18:47
17-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:17:9-55
18    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
18-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:19:5-75
18-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:19:22-72
19    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
19-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:20:5-84
19-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:20:22-81
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:21:5-68
20-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:21:22-65
21    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
21-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:22:5-73
21-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:22:22-70
22    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
22-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:23:5-79
22-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:23:22-76
23    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
23-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:24:5-80
23-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:24:22-77
24    <uses-permission android:name="android.permission.USES_POLICY_FORCE_LOCK" />
24-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:25:5-81
24-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:25:22-78
25    <uses-permission android:name="android.permission.BLUETOOTH" />
25-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:26:5-68
25-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:26:22-65
26    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
26-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:27:5-74
26-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:27:22-71
27    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
27-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:28:5-77
27-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:28:22-74
28    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
28-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:29:5-78
28-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:29:22-75
29    <uses-permission android:name="android.permission.CAMERA" />
29-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:30:5-65
29-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:30:22-62
30    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
30-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:31:5-76
30-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:31:22-73
31    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
31-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:32:5-79
31-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:32:22-76
32    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
32-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:33:5-76
32-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:33:22-73
33    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
33-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:34:5-95
33-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:34:22-92
34    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
34-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:29:5-78
34-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:29:22-75
35    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
35-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:36:5-37:47
35-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:36:22-82
36
37    <permission
37-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.antiy.medr.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.antiy.medr.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
42
43    <application
43-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:40:5-141:19
44        android:name="com.antiy.medr.app.App"
44-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:41:9-32
45        android:allowBackup="false"
45-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:42:9-36
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\64ca2416e8139516b55c483e6411564c\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
47        android:debuggable="true"
48        android:directBootAware="true"
48-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:43:9-39
49        android:extractNativeLibs="true"
49-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:44:9-41
50        android:icon="@mipmap/ic_launcher"
50-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:45:9-43
51        android:label="@string/app_name"
51-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:46:9-41
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:47:9-54
53        android:supportsRtl="true"
53-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:48:9-35
54        android:theme="@style/Theme.Medr" >
54-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:49:9-42
55        <service
55-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:50:9-57:19
56            android:name="com.antiy.medr.service.KeepAliveService"
56-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:51:13-53
57            android:enabled="true"
57-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:52:13-35
58            android:exported="true" >
58-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:53:13-36
59            <intent-filter>
59-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:54:13-56:29
60                <action android:name="com.antiy.medr.service.KeepAliveService" />
60-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:55:17-82
60-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:55:25-79
61            </intent-filter>
62        </service>
63        <service
63-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:58:9-61:39
64            android:name="com.antiy.medr.device.LockScreenService"
64-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:59:13-53
65            android:enabled="true"
65-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:60:13-35
66            android:exported="true" />
66-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:61:13-36
67
68        <receiver
68-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:63:9-72:20
69            android:name="com.antiy.medr.auth.AuthReceiver"
69-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:64:13-46
70            android:enabled="true"
70-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:65:13-35
71            android:exported="true" >
71-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:66:13-36
72            <intent-filter>
72-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:67:13-71:29
73                <action android:name="android.intent.action.SCREEN_ON" />
73-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:68:17-74
73-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:68:25-71
74                <action android:name="com.cetc30.auth.logout" />
74-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:69:17-65
74-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:69:25-62
75                <action android:name="com.cetc30.auth.login" />
75-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:70:17-64
75-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:70:25-61
76            </intent-filter>
77        </receiver>
78
79        <activity
79-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:74:9-76:39
80            android:name="com.antiy.medr.ui.SplashActivity"
80-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:75:13-46
81            android:exported="true" />
81-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:76:13-36
82        <activity
82-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:77:9-82:54
83            android:name="com.antiy.medr.ui.TerminalWatchActivity"
83-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:78:13-53
84            android:configChanges="keyboardHidden|orientation|screenSize"
84-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:79:13-74
85            android:exported="false"
85-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:80:13-37
86            android:label="@string/terminal_security_control"
86-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:81:13-62
87            android:screenOrientation="fullSensor" />
87-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:82:13-51
88        <activity
88-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:83:9-94:20
89            android:name="com.antiy.medr.ui.MainActivity"
89-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:84:13-44
90            android:configChanges="keyboardHidden|orientation|screenSize"
90-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:85:13-74
91            android:exported="true"
91-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:86:13-36
92            android:launchMode="singleTask"
92-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:87:13-44
93            android:screenOrientation="fullSensor" >
93-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:88:13-51
94            <intent-filter>
94-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:89:13-93:29
95                <action android:name="android.intent.action.MAIN" />
95-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:90:17-69
95-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:90:25-66
96
97                <category android:name="android.intent.category.LAUNCHER" />
97-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:92:17-77
97-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:92:27-74
98            </intent-filter>
99        </activity>
100
101        <!--
102            <service
103            android:name=".AccessService"
104            android:enabled="true"
105            android:exported="true"
106            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
107            >
108
109            <intent-filter>
110                <action android:name="android.accessibilityservice.AccessibilityService" />
111            </intent-filter>
112            <meta-data
113                android:name="android.accessibilityservice"
114                android:resource="@xml/service_config"/>
115        </service>
116        -->
117
118        <receiver
118-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:111:9-123:20
119            android:name="com.antiy.medr.device.AdminReceiver"
119-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:112:13-49
120            android:configChanges="keyboardHidden|orientation|screenSize"
120-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:113:13-74
121            android:label="@string/app_name"
121-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:114:13-45
122            android:permission="android.permission.BIND_DEVICE_ADMIN" >
122-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:115:13-70
123            <meta-data
123-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:116:13-118:56
124                android:name="android.app.device_admin"
124-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:117:17-56
125                android:resource="@xml/device_admin" />
125-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:118:17-53
126
127            <intent-filter>
127-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:120:13-122:29
128                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
128-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:121:17-82
128-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:121:25-79
129            </intent-filter>
130        </receiver> <!-- <activity -->
131        <!-- android:name="com.cetc30.auth.activity.SplashActivity" -->
132        <!-- android:exported="true" -->
133        <!-- android:taskAffinity="com.cetc30.auth" -->
134        <!-- tools:ignore="MissingClass" /> -->
135        <!-- <activity -->
136        <!-- android:name="com.cetc30.auth.activity.LoginActivity" -->
137        <!-- android:exported="true" -->
138        <!-- android:taskAffinity="com.cetc30.auth" -->
139        <!-- tools:ignore="MissingClass" /> &lt;!&ndash; <activity &ndash;&gt; -->
140        <!-- android:name="com.cetc30.auth.activity.ConfirmLoginActivity" -->
141        <!-- android:exported="true" -->
142        <!-- android:taskAffinity="com.cetc30.auth" -->
143        <!-- tools:ignore="MissingClass" /> -->
144        <activity
144-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:137:9-139:39
145            android:name="com.cetc30.auth.sdk.api.AgentActivity"
145-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:138:13-65
146            android:exported="true" />
146-->D:\Projects\Android\lang-ya-shan-1.0\app\src\main\AndroidManifest.xml:139:13-36
147
148        <service android:name="org.xplugin.core.app.ServicesProxy" />
148-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-70
148-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-67
149
150        <provider
150-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-14:50
151            android:name="org.xplugin.core.app.ContentProviderProxy"
151-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-69
152            android:authorities="com.antiy.medr.xPlugin.Provider"
152-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-68
153            android:exported="true"
153-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-36
154            android:grantUriPermissions="true" />
154-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-47
155
156        <activity
156-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-20:45
157            android:name="org.xplugin.tpl.DefaultActivity"
157-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-59
158            android:configChanges="screenSize|keyboardHidden|orientation"
158-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-74
159            android:exported="false"
159-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-37
160            android:launchMode="standard" />
160-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-42
161        <activity
161-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-26:64
162            android:name="org.xplugin.tpl.DefaultActivity_t"
162-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-61
163            android:configChanges="screenSize|keyboardHidden|orientation"
163-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-74
164            android:exported="false"
164-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-37
165            android:launchMode="standard"
165-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:13-42
166            android:theme="@android:style/Theme.Translucent" /> <!-- singleTop Activities -->
166-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:13-61
167        <activity
167-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-33:46
168            android:name="org.xplugin.tpl.SingleTopActivity_1"
168-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-63
169            android:configChanges="screenSize|keyboardHidden|orientation"
169-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-74
170            android:exported="false"
170-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:32:13-37
171            android:launchMode="singleTop" />
171-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:33:13-43
172        <activity
172-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:9-38:46
173            android:name="org.xplugin.tpl.SingleTopActivity_2"
173-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:13-63
174            android:configChanges="screenSize|keyboardHidden|orientation"
174-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:13-74
175            android:exported="false"
175-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:13-37
176            android:launchMode="singleTop" />
176-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:38:13-43
177        <activity
177-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:9-43:46
178            android:name="org.xplugin.tpl.SingleTopActivity_3"
178-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:13-63
179            android:configChanges="screenSize|keyboardHidden|orientation"
179-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:13-74
180            android:exported="false"
180-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:13-37
181            android:launchMode="singleTop" />
181-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:13-43
182        <activity
182-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:9-48:46
183            android:name="org.xplugin.tpl.SingleTopActivity_4"
183-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:45:13-63
184            android:configChanges="screenSize|keyboardHidden|orientation"
184-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:13-74
185            android:exported="false"
185-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:47:13-37
186            android:launchMode="singleTop" />
186-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:48:13-43
187        <activity
187-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:49:9-53:46
188            android:name="org.xplugin.tpl.SingleTopActivity_5"
188-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:50:13-63
189            android:configChanges="screenSize|keyboardHidden|orientation"
189-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:51:13-74
190            android:exported="false"
190-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:52:13-37
191            android:launchMode="singleTop" />
191-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:53:13-43
192        <activity
192-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:54:9-59:64
193            android:name="org.xplugin.tpl.SingleTopActivity_1_t"
193-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:55:13-65
194            android:configChanges="screenSize|keyboardHidden|orientation"
194-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:56:13-74
195            android:exported="false"
195-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:57:13-37
196            android:launchMode="singleTop"
196-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:58:13-43
197            android:theme="@android:style/Theme.Translucent" />
197-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:59:13-61
198        <activity
198-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:60:9-65:64
199            android:name="org.xplugin.tpl.SingleTopActivity_2_t"
199-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:61:13-65
200            android:configChanges="screenSize|keyboardHidden|orientation"
200-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:62:13-74
201            android:exported="false"
201-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:63:13-37
202            android:launchMode="singleTop"
202-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:64:13-43
203            android:theme="@android:style/Theme.Translucent" />
203-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:65:13-61
204        <activity
204-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:66:9-71:64
205            android:name="org.xplugin.tpl.SingleTopActivity_3_t"
205-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:67:13-65
206            android:configChanges="screenSize|keyboardHidden|orientation"
206-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:13-74
207            android:exported="false"
207-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:69:13-37
208            android:launchMode="singleTop"
208-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:70:13-43
209            android:theme="@android:style/Theme.Translucent" />
209-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:71:13-61
210        <activity
210-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:72:9-77:64
211            android:name="org.xplugin.tpl.SingleTopActivity_4_t"
211-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:73:13-65
212            android:configChanges="screenSize|keyboardHidden|orientation"
212-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:74:13-74
213            android:exported="false"
213-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:75:13-37
214            android:launchMode="singleTop"
214-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:76:13-43
215            android:theme="@android:style/Theme.Translucent" />
215-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:77:13-61
216        <activity
216-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:78:9-83:64
217            android:name="org.xplugin.tpl.SingleTopActivity_5_t"
217-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:79:13-65
218            android:configChanges="screenSize|keyboardHidden|orientation"
218-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:80:13-74
219            android:exported="false"
219-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:81:13-37
220            android:launchMode="singleTop"
220-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:82:13-43
221            android:theme="@android:style/Theme.Translucent" /> <!-- singleTask Activities -->
221-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:83:13-61
222        <activity
222-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:86:9-90:47
223            android:name="org.xplugin.tpl.SingleTaskActivity_1"
223-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:87:13-64
224            android:configChanges="screenSize|keyboardHidden|orientation"
224-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:88:13-74
225            android:exported="false"
225-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:89:13-37
226            android:launchMode="singleTask" />
226-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:90:13-44
227        <activity
227-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:91:9-95:47
228            android:name="org.xplugin.tpl.SingleTaskActivity_2"
228-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:92:13-64
229            android:configChanges="screenSize|keyboardHidden|orientation"
229-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:93:13-74
230            android:exported="false"
230-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:94:13-37
231            android:launchMode="singleTask" />
231-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:95:13-44
232        <activity
232-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:96:9-100:47
233            android:name="org.xplugin.tpl.SingleTaskActivity_3"
233-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:97:13-64
234            android:configChanges="screenSize|keyboardHidden|orientation"
234-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:98:13-74
235            android:exported="false"
235-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:99:13-37
236            android:launchMode="singleTask" />
236-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:100:13-44
237        <activity
237-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:101:9-105:47
238            android:name="org.xplugin.tpl.SingleTaskActivity_4"
238-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:102:13-64
239            android:configChanges="screenSize|keyboardHidden|orientation"
239-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:103:13-74
240            android:exported="false"
240-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:104:13-37
241            android:launchMode="singleTask" />
241-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:105:13-44
242        <activity
242-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:106:9-110:47
243            android:name="org.xplugin.tpl.SingleTaskActivity_5"
243-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:107:13-64
244            android:configChanges="screenSize|keyboardHidden|orientation"
244-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:108:13-74
245            android:exported="false"
245-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:109:13-37
246            android:launchMode="singleTask" />
246-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:110:13-44
247        <activity
247-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:111:9-116:64
248            android:name="org.xplugin.tpl.SingleTaskActivity_1_t"
248-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:112:13-66
249            android:configChanges="screenSize|keyboardHidden|orientation"
249-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:113:13-74
250            android:exported="false"
250-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:114:13-37
251            android:launchMode="singleTask"
251-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:115:13-44
252            android:theme="@android:style/Theme.Translucent" />
252-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:116:13-61
253        <activity
253-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:117:9-122:64
254            android:name="org.xplugin.tpl.SingleTaskActivity_2_t"
254-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:118:13-66
255            android:configChanges="screenSize|keyboardHidden|orientation"
255-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:119:13-74
256            android:exported="false"
256-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:120:13-37
257            android:launchMode="singleTask"
257-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:121:13-44
258            android:theme="@android:style/Theme.Translucent" />
258-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:122:13-61
259        <activity
259-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:123:9-128:64
260            android:name="org.xplugin.tpl.SingleTaskActivity_3_t"
260-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:124:13-66
261            android:configChanges="screenSize|keyboardHidden|orientation"
261-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:125:13-74
262            android:exported="false"
262-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:126:13-37
263            android:launchMode="singleTask"
263-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:127:13-44
264            android:theme="@android:style/Theme.Translucent" />
264-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:128:13-61
265        <activity
265-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:129:9-134:64
266            android:name="org.xplugin.tpl.SingleTaskActivity_4_t"
266-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:130:13-66
267            android:configChanges="screenSize|keyboardHidden|orientation"
267-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:131:13-74
268            android:exported="false"
268-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:132:13-37
269            android:launchMode="singleTask"
269-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:133:13-44
270            android:theme="@android:style/Theme.Translucent" />
270-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:134:13-61
271        <activity
271-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:135:9-140:64
272            android:name="org.xplugin.tpl.SingleTaskActivity_5_t"
272-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:136:13-66
273            android:configChanges="screenSize|keyboardHidden|orientation"
273-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:137:13-74
274            android:exported="false"
274-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:138:13-37
275            android:launchMode="singleTask"
275-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:139:13-44
276            android:theme="@android:style/Theme.Translucent" /> <!-- singleInstance Activities -->
276-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:140:13-61
277        <activity
277-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:143:9-147:51
278            android:name="org.xplugin.tpl.SingleInstanceActivity_1"
278-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:144:13-68
279            android:configChanges="screenSize|keyboardHidden|orientation"
279-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:145:13-74
280            android:exported="false"
280-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:146:13-37
281            android:launchMode="singleInstance" />
281-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:147:13-48
282        <activity
282-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:148:9-152:51
283            android:name="org.xplugin.tpl.SingleInstanceActivity_2"
283-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:149:13-68
284            android:configChanges="screenSize|keyboardHidden|orientation"
284-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:150:13-74
285            android:exported="false"
285-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:151:13-37
286            android:launchMode="singleInstance" />
286-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:152:13-48
287        <activity
287-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:153:9-157:51
288            android:name="org.xplugin.tpl.SingleInstanceActivity_3"
288-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:154:13-68
289            android:configChanges="screenSize|keyboardHidden|orientation"
289-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:155:13-74
290            android:exported="false"
290-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:156:13-37
291            android:launchMode="singleInstance" />
291-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:157:13-48
292        <activity
292-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:158:9-162:51
293            android:name="org.xplugin.tpl.SingleInstanceActivity_4"
293-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:159:13-68
294            android:configChanges="screenSize|keyboardHidden|orientation"
294-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:160:13-74
295            android:exported="false"
295-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:161:13-37
296            android:launchMode="singleInstance" />
296-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:162:13-48
297        <activity
297-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:163:9-167:51
298            android:name="org.xplugin.tpl.SingleInstanceActivity_5"
298-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:164:13-68
299            android:configChanges="screenSize|keyboardHidden|orientation"
299-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:165:13-74
300            android:exported="false"
300-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:166:13-37
301            android:launchMode="singleInstance" />
301-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:167:13-48
302        <activity
302-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:168:9-173:64
303            android:name="org.xplugin.tpl.SingleInstanceActivity_1_t"
303-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:169:13-70
304            android:configChanges="screenSize|keyboardHidden|orientation"
304-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:170:13-74
305            android:exported="false"
305-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:171:13-37
306            android:launchMode="singleInstance"
306-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:172:13-48
307            android:theme="@android:style/Theme.Translucent" />
307-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:173:13-61
308        <activity
308-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:174:9-179:64
309            android:name="org.xplugin.tpl.SingleInstanceActivity_2_t"
309-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:175:13-70
310            android:configChanges="screenSize|keyboardHidden|orientation"
310-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:176:13-74
311            android:exported="false"
311-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:177:13-37
312            android:launchMode="singleInstance"
312-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:178:13-48
313            android:theme="@android:style/Theme.Translucent" />
313-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:179:13-61
314        <activity
314-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:180:9-185:64
315            android:name="org.xplugin.tpl.SingleInstanceActivity_3_t"
315-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:181:13-70
316            android:configChanges="screenSize|keyboardHidden|orientation"
316-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:182:13-74
317            android:exported="false"
317-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:183:13-37
318            android:launchMode="singleInstance"
318-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:184:13-48
319            android:theme="@android:style/Theme.Translucent" />
319-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:185:13-61
320        <activity
320-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:186:9-191:64
321            android:name="org.xplugin.tpl.SingleInstanceActivity_4_t"
321-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:187:13-70
322            android:configChanges="screenSize|keyboardHidden|orientation"
322-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:188:13-74
323            android:exported="false"
323-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:189:13-37
324            android:launchMode="singleInstance"
324-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:190:13-48
325            android:theme="@android:style/Theme.Translucent" />
325-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:191:13-61
326        <activity
326-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:192:9-197:64
327            android:name="org.xplugin.tpl.SingleInstanceActivity_5_t"
327-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:193:13-70
328            android:configChanges="screenSize|keyboardHidden|orientation"
328-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:194:13-74
329            android:exported="false"
329-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:195:13-37
330            android:launchMode="singleInstance"
330-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:196:13-48
331            android:theme="@android:style/Theme.Translucent" />
331-->[:xplugin] D:\Projects\Android\lang-ya-shan-1.0\xplugin\build\intermediates\merged_manifest\debug\AndroidManifest.xml:197:13-61
332        <activity
332-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:10:9-14:75
333            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
333-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:11:13-83
334            android:configChanges="orientation|keyboardHidden|screenSize"
334-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:12:13-74
335            android:theme="@style/ActivityTranslucent"
335-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:13:13-55
336            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
336-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:14:13-72
337        <activity
337-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:15:9-20:75
338            android:name="com.blankj.utilcode.util.UtilsTransActivity"
338-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:16:13-71
339            android:configChanges="orientation|keyboardHidden|screenSize"
339-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:17:13-74
340            android:multiprocess="true"
340-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:18:13-40
341            android:theme="@style/ActivityTranslucent"
341-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:19:13-55
342            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
342-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:20:13-72
343
344        <provider
344-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:22:9-30:20
345            android:name="com.blankj.utilcode.util.UtilsFileProvider"
345-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:23:13-70
346            android:authorities="com.antiy.medr.utilcode.fileprovider"
346-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:24:13-73
347            android:exported="false"
347-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:25:13-37
348            android:grantUriPermissions="true" >
348-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:26:13-47
349            <meta-data
349-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:27:13-29:68
350                android:name="android.support.FILE_PROVIDER_PATHS"
350-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:28:17-67
351                android:resource="@xml/util_code_provider_paths" />
351-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:29:17-65
352        </provider>
353
354        <service
354-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:32:9-38:19
355            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
355-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:33:13-81
356            android:exported="false" >
356-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:34:13-37
357            <intent-filter>
357-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:35:13-37:29
358                <action android:name="com.antiy.medr.messenger" />
358-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:36:17-69
358-->[com.blankj:utilcodex:1.30.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\de3f047d6aa963d158be6bf4db817c84\transformed\jetified-utilcodex-1.30.6\AndroidManifest.xml:36:25-66
359            </intent-filter>
360        </service>
361        <service
361-->[androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:25:9-28:40
362            android:name="androidx.room.MultiInstanceInvalidationService"
362-->[androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:26:13-74
363            android:directBootAware="true"
363-->[androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:27:13-43
364            android:exported="false" />
364-->[androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\9d58114e95c108cd824c74369c7129c7\transformed\room-runtime-2.4.2\AndroidManifest.xml:28:13-37
365
366        <provider
366-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
367            android:name="androidx.startup.InitializationProvider"
367-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
368            android:authorities="com.antiy.medr.androidx-startup"
368-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
369            android:exported="false" >
369-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
370            <meta-data
370-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
371                android:name="androidx.emoji2.text.EmojiCompatInitializer"
371-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
372                android:value="androidx.startup" />
372-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\30e155b2d817871594ba6b000cd1f7b5\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
373            <meta-data
373-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
374                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
374-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
375                android:value="androidx.startup" />
375-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\c7ddd9831a35fa33bd88bba06eb0c265\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
376            <meta-data
376-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
377                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
378                android:value="androidx.startup" />
378-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
379        </provider>
380
381        <receiver
381-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
382            android:name="androidx.profileinstaller.ProfileInstallReceiver"
382-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
383            android:directBootAware="false"
383-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
384            android:enabled="true"
384-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
385            android:exported="true"
385-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
386            android:permission="android.permission.DUMP" >
386-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
387            <intent-filter>
387-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
388                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
388-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
388-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
389            </intent-filter>
390            <intent-filter>
390-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
391                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
391-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
391-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
392            </intent-filter>
393            <intent-filter>
393-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
394                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
394-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
394-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
395            </intent-filter>
396            <intent-filter>
396-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
397                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
397-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
397-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cc79aacd83152bc9ceed2f106f20960c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
398            </intent-filter>
399        </receiver>
400    </application>
401
402</manifest>
