package com.antiy.medr.base;

import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableSource;
import io.reactivex.rxjava3.functions.Function;
import timber.log.Timber;

public class RetryWithDelay implements
        Function<Observable<Throwable>, ObservableSource<?>> {

    private static final int MAX_RETRIES = Integer.MAX_VALUE;
    private static final int RETRY_DELAY_SECOND = 5;
    private final int maxRetries;
    private final int retryDelaySecond;
    private int retryCount;

    public RetryWithDelay() {
        this.maxRetries = MAX_RETRIES;
        this.retryDelaySecond = RETRY_DELAY_SECOND;
    }

    public RetryWithDelay(int maxRetries, int retryDelaySecond) {
        this.maxRetries = maxRetries;
        this.retryDelaySecond = retryDelaySecond;
    }

    @Override
    public ObservableSource<?> apply(@NonNull Observable<Throwable> throwableObservable) throws Exception {
        return throwableObservable
                .flatMap((Function<Throwable, ObservableSource<?>>) throwable -> {
                    if (++retryCount <= maxRetries) {
                        Timber.d("Observable get error, it will try after " + retryDelaySecond
                                + " second, retry count " + retryCount + throwable);
                        return Observable.timer(retryDelaySecond,
                                TimeUnit.SECONDS);
                    }
                    return Observable.error(throwable);
                });
    }
}