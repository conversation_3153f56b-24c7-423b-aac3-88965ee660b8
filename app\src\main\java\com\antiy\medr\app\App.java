package com.antiy.medr.app;

import static com.antiy.medr.model.StatusEnum.ENABLE;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.widget.Toast;

import com.antiy.medr.Constants;
import com.antiy.medr.auth.AuthApiManager;
import com.antiy.medr.base.DefaultObserver;
import com.antiy.medr.data.source.Repository;
import com.antiy.medr.device.DevInterfaceManager;
import com.antiy.medr.device.DeviceBroadcastReceiver;
import com.antiy.medr.device.DeviceConfigHelper;
import com.antiy.medr.device.controller.BluetoothController;
import com.antiy.medr.device.controller.IController;
import com.antiy.medr.device.controller.IptablesController;
import com.antiy.medr.device.controller.SdCardController;
import com.antiy.medr.device.controller.UsbController;
import com.antiy.medr.device.controller.WifiController;
import com.antiy.medr.model.ConfigModel;
import com.antiy.medr.network.Api;
import com.antiy.medr.network.WebSocketManager;
import com.antiy.medr.plugin.XPluginHelper;
import com.antiy.medr.util.CrashHandler;
import com.blankj.utilcode.util.GsonUtils;
import com.cetc30.auth.sdk.api.v3.AgentAPIV3;
import com.cetc30.auth.sdk.utils.ServerUtils;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.GsonBuilder;
import com.rxjava.rxlife.RxLife;

import org.xplugin.core.PluginRuntime;
import org.xplugin.core.PluginRuntimeListener;
import org.xplugin.core.ctx.Plugin;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Maybe;
import io.reactivex.rxjava3.plugins.RxJavaPlugins;
import io.reactivex.rxjava3.schedulers.Schedulers;
import timber.log.Timber;

/**
 * Created on 2022/3/31.
 *
 * <AUTHOR>
 */
public class App extends Application {
    static App sInstance;
    private Boolean isBootLocked;
    @Override
    public void onCreate() {
        super.onCreate();
        sInstance = this;
        setBootLocked(false);
        initAuthApi();
        Timber.plant(new Timber.DebugTree());
        RxJavaPlugins.setErrorHandler(Timber::d);
        DeviceBroadcastReceiver.register(this);
        loadPlugin();
        configGlobalGson();
        new CrashHandler().init(this);
        CrashHandler.is_Debug = false;
//        //临时：如果为 mode=2 则先改为 1
//        try {
//            final DevInterface devInterface = DevInterfaceManager.getInstance();
//            final String[] bluetoothPolicies = devInterface.getBluetoothPolicies();
//            final int mode = Integer.parseInt(bluetoothPolicies[0]);
//            if (mode == 2) {
//                devInterface.setBluetoothPolicies(1, new String[]{});
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }


        //tmp: 默认禁止的选项
        DevInterfaceManager.getInstance()
                .setWlanPolicies(IController.DISABLE);
        DevInterfaceManager.getInstance()
                .setDataConnectivityPolicies(IController.DISABLE);
        DevInterfaceManager.getInstance()
                .setStatusBarNotificationDisabled(true);


        //iptables 设置

        new Thread() {
            @Override
            public void run() {
                super.run();
                try {
                    //默认需要放开
 /*                   try {
                        File file = new File(App.this.getCacheDir(), "cacheUtils");
                        //首次安装时，没有缓存和网络时
                        if (!file.exists() || file.listFiles().length == 0) {
                            new BluetoothController().disable();
                            new SdCardController().disable();
                            new UsbController().disable();
                            new WifiController().disable();
                        }

                    } catch (Exception e) {
                        CrashHandler.addlog(e.getMessage());
                    }*/


                    sleep(10000);
                    //开机自启需要延迟加载
//                    AuthApiManager.getInstance().getApi().screenLock();

                    DevInterfaceManager.getInstance()
                            .executeShellToSetIptables("-F");

                    //设置服务器以及本地
                    DevInterfaceManager.getInstance()
                            .executeShellToSetIptables("-P OUTPUT DROP");
                    DevInterfaceManager.getInstance()
                            .executeShellToSetIptables("-P INPUT DROP");


                    String ip = ServerUtils.getServerIp();

                    if (ip != null) {
                        DevInterfaceManager.getInstance()
                                .executeShellToSetIptables("-I INPUT -m tcp -p tcp -s " + ip + " -j ACCEPT");
                        DevInterfaceManager.getInstance()
                                .executeShellToSetIptables("-I OUTPUT -m tcp -p tcp -d " + ip + " -j ACCEPT");
                    }

                    DevInterfaceManager.getInstance()
                            .executeShellToSetIptables("-I INPUT -m tcp -p tcp -s 127.0.0.1 -j ACCEPT");
                    DevInterfaceManager.getInstance()
                            .executeShellToSetIptables("-I OUTPUT -m tcp -p tcp -d 127.0.0.1 -j ACCEPT");
                    //加载离线配置
/*                    Repository.getInstance().getConfig()
                            .subscribeOn(Schedulers.io())
                            .subscribe(configModel -> {
                                new DeviceConfigHelper(configModel).process();
                            });*/
                    initWebSocket();

            /*        Repository.getInstance()
                            .getConfig()
                            .subscribeOn(Schedulers.io())
                            .subscribe(new DefaultObserver<ConfigModel>() {
                                @Override
                                public void onSuccess(ConfigModel configModel) {
                                    new DeviceConfigHelper(configModel)
                                            .process();
                                }
                            });*/

                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }.start();



/*        Repository.getInstance()
                .getWsIp()
                .subscribeOn(Schedulers.io())
                .subscribe(new DefaultObserver<String>() {
                    @Override
                    public void onSuccess(String ip) {
//                        Timber.i("本地配置 ip: %s", ip);
                        ToastUtils.showShort("本地配置 ip: %s", ip);
                        DevInterfaceManager.getInstance()
                                .executeShellToSetIptables("-I OUTPUT -j DROP");
//                        DevInterfaceManager.getInstance()
//                                .executeShellToSetIptables("-I OUTPUT -m tcp -p tcp -d ************* --dport 8080 -j ACCEPT");
                        DevInterfaceManager.getInstance()
                                .executeShellToSetIptables("-I OUTPUT -m tcp -p tcp -d " + ip + " --dport " + Api.PORT + " -j ACCEPT");
                        DevInterfaceManager.getInstance()
                                .executeShellToSetIptables("-I OUTPUT -d 127.0.0.1 -j ACCEPT");
                    }
                });*/


    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        initPlugin();
    }

    private void initWebSocket() {

        String serverip = ServerUtils.getServerIp();
        if (serverip != null && serverip.length() != 0 ) {
            Repository.getInstance().saveWsIp(serverip);
            WebSocketManager.getInstance().connect(Api.getWsLinkFromIp(serverip));
        }

/*        Repository.getInstance()
                .getWsIp()
                .subscribeOn(Schedulers.io())
                .subscribe(s -> WebSocketManager.getInstance().connect(Api.getWsLinkFromIp(s)));*/
    }

    private void initAuthApi() {
        AgentAPIV3.init(this, getApplicationContext().getPackageName(), null);
        AuthApiManager.getInstance().getApi().init(getPackageName(), null);
    }

    /**
     * 配置全局 GsonUtils 将下划线转为驼峰
     */
    private void configGlobalGson() {
        GsonUtils.setGsonDelegate(new GsonBuilder()
                .setPrettyPrinting()
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
                .create());
    }

    private void loadPlugin() {
        XPluginHelper pluginHelper = new XPluginHelper.Builder()
                .addPlugin(Constants.AUTH_PLUGIN_PATH, Constants.AUTH_PLUGIN_PKG)
                .addPlugin(Constants.WATERMARK_PLUGIN_PATH, Constants.WATERMARK_PLUGIN_PKG)
                .addPlugin(Constants.ANTIVIRUS_PLUGIN_PATH, Constants.ANTIVIRUS_PLUGIN_PKG)
//                .addPlugin(Constants.KX_PLUGIN_PATH, Constants.KX_PLUGIN_PKG)
                .setPluginEvent(() -> {
                    //插件加载完成启动水印的 Activity
                    Completable.fromAction(() -> {
                                Intent intent = new Intent();
                                intent.putExtra("packagename", getPackageName());
                                intent.setClassName("com.hrgz.watermark", "com.hrgz.watermark" + ".MainActivity");
                                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                startActivity(intent);
                            })
                            .subscribeOn(AndroidSchedulers.mainThread())
                            .subscribe();

                }).build();
        pluginHelper.excuteInstall();
    }

    private void initPlugin() {
        PluginRuntime.init(this, new PluginRuntimeListener() {
            @Override
            public boolean isDebug() {
                return true;
            }

            @Override
            public Class<?> getHostPluginEntry() {
                return null;
            }

            @Override
            public String getRuntimePkg() {
                return getPackageName();
            }

            @Override
            public boolean verifyPluginFile(File pluginFile) {
                // 校验文件签名
                return true;
            }

            @Override
            public void onHostInitialised(boolean hasError) {
                Log.d("PluginRuntime", "hasError: " + hasError);
            }

            @Override
            public void onPluginsLoaded(Map<String, Plugin> plugins) {
                Log.d("PluginRuntime", "plugins: " + plugins.keySet());
            }

            @Override
            public void onPluginsLoadError(Throwable ex, boolean isCallbackError) {
                Log.e("PluginRuntime", ex.getMessage(), ex);
            }
        });
    }
    public static App getInstance() {
        return sInstance;
    }

    public void setBootLocked(Boolean bootLocked) {
        isBootLocked = bootLocked;
    }
    public Boolean getIsBootLocked(){
        return isBootLocked;
    }
}
