package com.antiy.medr;

import android.os.Environment;

import java.io.File;

/**
 * Created on 2022/4/7.
 *
 * <AUTHOR>
 */
public interface Constants {
    String AUTH_PLUGIN_PATH = Environment.getExternalStorageDirectory() + File.separator + "cetc30auth";
    String AUTH_PLUGIN_PKG = "com.cetc30.auth";

    String ANTIVIRUS_PLUGIN_PATH = Environment.getExternalStorageDirectory() + File.separator + "antivirus-release-unsigned-360";
    String ANTIVIRUS_PLUGIN_PKG = "com.qihoo360.mobilesafe.standalone.antivirus";

    String WATERMARK_PLUGIN_PATH = Environment.getExternalStorageDirectory() + File.separator + "WaterMark";
    String WATERMARK_PLUGIN_PKG = "com.hrgz.watermark";

    String KX_PLUGIN_PATH = Environment.getExternalStorageDirectory() + File.separator + "kxclient";
    String KX_PLUGIN_PKG = "kx.httc.com.kxclient";

    interface Cache{
        String CONFIG = "config";
        String WS_IP = "WS_URL";
    }

    interface Regex{
        String WS_REGEX = "^ws://(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d):([0-9]|[1-9]\\d{1,3}|[1-5]\\d{4}|6[0-4]\\d{3}|65[0-4]\\d{2}|655[0-2]\\d|6553[0-5])/\\S*$";
    }
}
