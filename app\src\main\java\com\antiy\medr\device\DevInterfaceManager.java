package com.antiy.medr.device;

import com.android.DevCtrl.DevInterface;
import com.blankj.utilcode.util.Utils;

/**
 * Created on 2022/3/31.
 *
 * <AUTHOR>
 */
public class DevInterfaceManager {
    private DevInterfaceManager() {
    }

    public static DevInterface getInstance() {
        return DeviceInfoHolder.INSTANCE;
    }

    private static class DeviceInfoHolder {
        private static final DevInterface INSTANCE = new DevInterface(Utils.getApp());
    }
}
