package com.antiy.medr.data.source;

import com.antiy.medr.model.ConfigModel;
import com.antiy.medr.model.EventLog;

import java.util.List;

import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Single;

/**
 * Created on 2022/4/2.
 *
 * <AUTHOR>
 */
public interface IRepository {
    /**
     * 保存配置
     * @param configModel
     * @return
     */
    Completable saveConfig(ConfigModel configModel);

    /**
     * 获取配置信息
     * @return
     */
    Observable<ConfigModel> getConfig();


    /**
     * 上传log信息
     * @param log
     * @return
     */
    Observable<Boolean> uploadLog(EventLog log);

    /**
     * 获取ws url
     * @return
     */
    Observable<String> getWsIp();

    /**
     * 保存ws url
     * @param url
     * @return
     */
    Completable saveWsIp(String url);

    /**
     * 获取所有违规事件日志
     * @return
     */
    Single<List<EventLog>> getAllEventLogs();

    /**
     * 删除违规事件日志
     * @param logs
     * @return
     */
    Completable deleteEventLog(EventLog... logs);

    /**
     * 30所上传数据
     * @param log
     * @return
     */
    Completable eventLog(EventLog log);
}
