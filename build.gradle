// Top-level build file where you can add configuration options common to all sub-projects/modules.
//buildscript {
//    ext {
//        agp_version = '8.0.2'
//    }
//    repositories {
//        maven{url 'https://maven.aliyun.com/nexus/content/groups/public/'}
//        maven{ url 'https://maven.aliyun.com/repository/google' }
//        maven { url 'https://jitpack.io' }
//        mavenCentral()
//        google()
//    }
//    dependencies {
//        classpath "com.android.tools.build:gradle:$agp_version"
//
//        // NOTE: Do not place your application dependencies here; they belong
//        // in the individual module build.gradle files
//    }
//}
//
//allprojects {
//    repositories {
//        maven{url 'https://maven.aliyun.com/nexus/content/groups/public/'}
//        maven{ url 'https://maven.aliyun.com/repository/google' }
//        maven { url 'https://jitpack.io' }
//        mavenCentral()
//        google()
//    }
//}
//
//task clean(type: Delete) {
//    delete rootProject.buildDir
//}
plugins {
    id 'com.android.application' version '8.0.2' apply false
    id 'com.android.library' version '8.0.2' apply false
}
