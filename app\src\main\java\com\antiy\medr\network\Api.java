package com.antiy.medr.network;

/**
 * Created on 2022/4/7.
 *
 * <AUTHOR>
 */
public final class Api {

    public static final String WS_IP = "************";
    public static final String PORT = "2222";
//    public static final String WS_URL = "ws://*************/ws_init";

    private Api() {
    }

    public static String getWsLinkFromIp(String ipAddress) {
        return "ws://" + ipAddress + ":" + PORT + "/ws_init";
    }

    /**
     * websocket相关接口
     */
    public interface WsInterface {
        /**
         * ws建立
         */
        String WS_INIT = "ws_initial";
        /**
         * 心跳接口
         */
        String HEART_BEAT = "ws_heartbeat";
        /**
         * 设备信息上传接口
         */
        String DEVICE_UPLOAD = "ws_devices";

        /**
         * 白名单
         */
        String WHITE_LIST = "wl";

        /**
         *
         */
        String AUTH = "auth";
    }
}
