package com.antiy.medr.device;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.IBinder;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;

import androidx.core.app.NotificationCompat;

import com.antiy.medr.R;

/**
 * 锁定悬浮窗服务
 * Created on 2022/6/16.
 *
 * <AUTHOR>
 */
public class LockScreenService extends Service {

    private static final int NOTIFICATION_ID = 1;
    private WindowManager mWindowManager;
    private View mWindowView;

    @Override
    public void onCreate() {
        super.onCreate();
        openWindow();
        //设置状态栏不允许下拉
        DevInterfaceManager.getInstance().setStatusBarNotificationDisabled(true);
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            startForeground(NOTIFICATION_ID, new Notification());
        } else {
            NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            if (manager != null) {
                NotificationChannel channel = new NotificationChannel("channel", "锁屏通知", NotificationManager.IMPORTANCE_MIN);
                manager.createNotificationChannel(channel);
                Notification notification = new NotificationCompat.Builder(this, "channel").build();
                startForeground(NOTIFICATION_ID, notification);
            }
        }
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mWindowManager != null && mWindowView != null) {
            mWindowManager.removeView(mWindowView);
        }
        //恢复状态栏下拉
        DevInterfaceManager.getInstance().setStatusBarNotificationDisabled(false);
        stopForeground(true);
    }

    private void openWindow() {
        mWindowManager = ((WindowManager) getApplicationContext()
                .getSystemService(Context.WINDOW_SERVICE));
        WindowManager.LayoutParams localLayoutParams = new WindowManager.LayoutParams();
        localLayoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ERROR;
        localLayoutParams.gravity = Gravity.TOP;
        localLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
        localLayoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        localLayoutParams.height = WindowManager.LayoutParams.MATCH_PARENT;
        localLayoutParams.format = PixelFormat.TRANSPARENT;
        mWindowView = LayoutInflater.from(this).inflate(R.layout.view_lock_window, null);
        mWindowManager.addView(mWindowView, localLayoutParams);
    }
}