package com.antiy.medr.device.controller;

import com.antiy.medr.model.StatusEnum;

import java.util.List;

/**
 * Created on 2022/4/7.
 *
 * <AUTHOR>
 */
public interface IController<T> {
    int DISABLE = 0;
    /**
     * 启用
     */
    void enable();

    /**
     * 禁用
     */
    void disable();

    /**
     * 其他操作
     * @param status
     */
    default void other(StatusEnum status) {

    }


    /**
     * 添加白名单
     * @param whiteList
     * @return
     */
    boolean setWhiteList(List<T> whiteList);

}
