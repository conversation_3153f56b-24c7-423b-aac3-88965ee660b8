package com.antiy.medr.network;

import android.os.Handler;
import android.os.HandlerThread;
import android.util.Pair;

import com.antiy.medr.Constants;
import com.antiy.medr.base.AvlException;
import com.antiy.medr.base.DefaultObserver;
import com.antiy.medr.data.source.Repository;
import com.antiy.medr.device.DevInterfaceManager;
import com.antiy.medr.device.DeviceConfigHelper;
import com.antiy.medr.model.BaseRequest;
import com.antiy.medr.model.BaseResponse;
import com.antiy.medr.model.ConfigModel;
import com.antiy.medr.model.DeviceModel;
import com.antiy.medr.util.CrashHandler;
import com.antiy.medr.util.SecurityLogger;
import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.Utils;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.security.SecureRandom;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLException;
import javax.net.ssl.X509TrustManager;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.functions.Function;
import io.reactivex.rxjava3.observables.GroupedObservable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okhttp3.logging.HttpLoggingInterceptor;
import timber.log.Timber;

/**
 * 升级版WebSocket管理器，支持SSL/TLS加密连接
 * Created on 2022/4/1.
 * Updated for SSL support on 2024/12/24.
 *
 * <AUTHOR>
 */
public class WebSocketManagerSSL {

    private final Handler mHandler;
    private final Object lock = new Object();
    private volatile boolean connected = false;
    private volatile String mUrl;
    private WebSocket mWebSocket;
    
    // SSL相关组件
    private SSLContext sslContext;
    private X509TrustManager trustManager;
    private HostnameVerifier hostnameVerifier;
    private CertificateManager certificateManager;
    
    // 连接重试相关
    private int sslRetryCount = 0;
    private long connectionStartTime = 0;

    private WebSocketManagerSSL() {
        final HandlerThread handlerThread = new HandlerThread("websocket looper thread");
        handlerThread.start();
        mHandler = new Handler(handlerThread.getLooper());
        
        // 初始化证书管理器
        certificateManager = CertificateManager.getInstance();
    }

    public static WebSocketManagerSSL getInstance() {
        return WebSocketHolder.INSTANCE;
    }

    public static String getUUID() {
        return DevInterfaceManager.getInstance().getSerialNumber();
    }

    /**
     * 建立WebSocket连接，支持SSL和非SSL
     * @param url WebSocket URL (ws:// 或 wss://)
     */
    public void connect(String url) {
        synchronized (lock) {
            connectionStartTime = System.currentTimeMillis();
            mHandler.removeCallbacksAndMessages(null);
            
            // 关闭现有连接
            if (connected && mWebSocket != null) {
                mWebSocket.cancel();
            }
            
            mUrl = url;
            
            try {
                // 记录连接尝试
                NetworkMetrics.recordConnectionAttempt();
                SecurityLogger.logSecurityEvent("CONNECTION_ATTEMPT", "URL: " + url);
                
                // 创建HTTP客户端
                OkHttpClient client = createHttpClient(url);
                
                // 创建WebSocket请求
                Request request = new Request.Builder()
                        .url(url)
                        .build();
                
                CommandListener commandListener = new CommandListener();
                client.newWebSocket(request, commandListener);
                
            } catch (Exception e) {
                Timber.e(e, "创建WebSocket连接失败");
                handleConnectionError(e);
            }
        }
    }

    /**
     * 创建HTTP客户端，根据URL类型配置SSL
     */
    private OkHttpClient createHttpClient(String url) throws Exception {
        HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor(new HttpLogger());
        interceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder()
                .pingInterval(10, TimeUnit.SECONDS)
                .addNetworkInterceptor(interceptor)
                .connectTimeout(Constants.Network.SSL_CONNECT_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .readTimeout(Constants.Network.SSL_READ_TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .writeTimeout(Constants.Network.SSL_WRITE_TIMEOUT_SECONDS, TimeUnit.SECONDS);

        // 如果是WSS连接，配置SSL
        if (url.startsWith("wss://")) {
            configureSsl(clientBuilder);
        }

        return clientBuilder.build();
    }

    /**
     * 配置SSL/TLS设置
     */
    private void configureSsl(OkHttpClient.Builder builder) throws Exception {
        try {
            Timber.i("开始配置SSL");
            
            // 创建信任管理器
            trustManager = certificateManager.createTrustManager(Utils.getApp());
            if (trustManager == null) {
                throw new Exception("无法创建信任管理器");
            }

            // 创建SSL上下文
            sslContext = SSLContext.getInstance(Constants.Network.SSL_PROTOCOL);
            sslContext.init(null, new javax.net.ssl.TrustManager[]{trustManager}, new SecureRandom());

            // 配置SSL套接字工厂
            builder.sslSocketFactory(sslContext.getSocketFactory(), trustManager);

            // 配置主机名验证器
            hostnameVerifier = certificateManager.createHostnameVerifier();
            builder.hostnameVerifier(hostnameVerifier);

            Timber.i("SSL配置完成");
            SecurityLogger.logSecurityEvent("SSL_CONFIGURED", "SSL配置成功");

        } catch (Exception e) {
            String errorMsg = "SSL配置失败: " + e.getMessage();
            Timber.e(e, errorMsg);
            SecurityLogger.logSecurityEvent("SSL_CONFIG_FAILED", errorMsg);
            
            // 根据验证模式决定是否抛出异常
            if (Constants.Network.CERT_VALIDATION_MODE.equals(Constants.Network.CERT_VALIDATION_STRICT)) {
                throw new Exception("SSL配置失败，无法建立安全连接", e);
            }
        }
    }

    /**
     * 发送文本消息
     */
    public Observable<Boolean> send(String text) {
        return Observable.defer(() -> Observable.create(emitter -> {
            synchronized (lock) {
                if (mWebSocket == null) {
                    emitter.onError(new AvlException("webSocket not connected!"));
                } else {
                    boolean success = mWebSocket.send(text);
                    emitter.onNext(success);
                    Timber.i("发送消息: %s", text);
                    emitter.onComplete();
                }
            }
        }));
    }

    /**
     * 发送结构化请求
     */
    public <T> Observable<Boolean> send(BaseRequest<T> request) {
        return Observable.defer(() -> Observable.just(GsonUtils.toJson(request))
                .flatMap(this::send));
    }

    /**
     * 处理连接错误
     */
    private void handleConnectionError(Throwable error) {
        if (error instanceof SSLException) {
            handleSslError((SSLException) error);
        } else {
            handleGeneralConnectionError(error);
        }
    }

    /**
     * 处理SSL相关错误
     */
    private void handleSslError(SSLException sslException) {
        String errorMessage = "SSL连接错误: " + sslException.getMessage();
        Timber.e(errorMessage);
        SecurityLogger.logSecurityEvent("SSL_ERROR", errorMessage);
        
        sslRetryCount++;
        
        if (sslRetryCount < Constants.Network.MAX_SSL_RETRY_ATTEMPTS) {
            // 重试SSL连接
            Timber.i("重试SSL连接，第%d次", sslRetryCount);
            mHandler.postDelayed(() -> connect(mUrl), Constants.Network.SSL_RETRY_DELAY_MS);
        } else if (Constants.Network.ENABLE_FALLBACK && mUrl.startsWith("wss://")) {
            // 回退到非SSL连接
            String fallbackUrl = mUrl.replace("wss://", "ws://")
                    .replace(":" + Constants.Network.SSL_PORT, ":" + Constants.Network.DEFAULT_PORT);
            Timber.w("SSL连接多次失败，回退到非SSL连接: %s", fallbackUrl);
            SecurityLogger.logSecurityEvent("SSL_FALLBACK", "回退到: " + fallbackUrl);
            connect(fallbackUrl);
        } else {
            // 无法连接
            SecurityLogger.logSecurityEvent("SSL_FAILED", "SSL连接最终失败");
            handleFinalConnectionFailure(sslException);
        }
    }

    /**
     * 处理一般连接错误
     */
    private void handleGeneralConnectionError(Throwable error) {
        Timber.e(error, "一般连接错误");
        SecurityLogger.logSecurityEvent("CONNECTION_ERROR", error.getMessage());
        
        // 应用未配置时使用离线配置
        if (DeviceConfigHelper.FIRST_PROCESS) {
            try {
                CrashHandler.addlog("FIRST_PROCESS:" + DeviceConfigHelper.FIRST_PROCESS);
                DeviceConfigHelper.FIRST_PROCESS = false;

                ConfigModel configModel = Repository.getInstance().getConfig().blockingFirst();
                CrashHandler.addlog("连接超时，开始使用缓存配置");
                CrashHandler.addlog(configModel.toString());
                DeviceConfigHelper.getInstance().process(configModel);
            } catch (Exception e) {
                e.printStackTrace();
                CrashHandler.addlog("===调用离线配置出错===");
                CrashHandler.addlog(e.getMessage());
            }
        }

        connected = false;
        mHandler.postDelayed(() -> connect(mUrl), CommandListener.RECONNECT_INTERVAL);
    }

    /**
     * 处理最终连接失败
     */
    private void handleFinalConnectionFailure(Throwable error) {
        connected = false;
        ToastUtils.showLong("网络连接失败，请检查网络设置");
        CrashHandler.addlog("最终连接失败: " + error.getMessage());
    }

    private static class WebSocketHolder {
        private static final WebSocketManagerSSL INSTANCE = new WebSocketManagerSSL();
    }

    /**
     * WebSocket事件监听器
     */
    private class CommandListener extends WebSocketListener {

        public static final int RECONNECT_INTERVAL = 5 * 1000;

        @Override
        public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
            super.onMessage(webSocket, text);
            Timber.i("收到消息:%s", text);
            mHandler.post(() -> operate(text));
        }

        @Override
        public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
            super.onOpen(webSocket, response);
            long duration = System.currentTimeMillis() - connectionStartTime;
            
            Timber.i("WebSocket连接成功");
            SecurityLogger.logSslConnection(mUrl, true, duration);
            NetworkMetrics.recordConnectionSuccess(duration);
            ToastUtils.showShort("与服务端连接成功");
            
            connected = true;
            sslRetryCount = 0; // 重置重试计数
            
            synchronized (lock) {
                mWebSocket = webSocket;
            }
            
            sendDeviceInfo();
            mHandler.removeCallbacksAndMessages(null);
        }

        @Override
        public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, @Nullable Response response) {
            super.onFailure(webSocket, t, response);
            long duration = System.currentTimeMillis() - connectionStartTime;
            
            Timber.e("连接失败 %s", t.toString());
            SecurityLogger.logSslConnection(mUrl, false, duration);
            
            connected = false;
            handleConnectionError(t);
        }

        @Override
        public void onClosed(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
            super.onClosed(webSocket, code, reason);
            Timber.e("连接关闭: code=%d, reason=%s", code, reason);
            SecurityLogger.logSecurityEvent("CONNECTION_CLOSED", "Code: " + code + ", Reason: " + reason);
            connected = false;
        }

        /**
         * 处理服务端消息
         */
        private void operate(String text) {
            CrashHandler.addlog(text);
            Observable.create((ObservableOnSubscribe<Pair<String, Object>>) emitter -> {
                final BaseResponse<Object> response = GsonUtils.fromJson(text, new TypeToken<BaseResponse<Object>>() {
                }.getType());
                if (response.getCode() == 0) {
                    emitter.onNext(Pair.create(response.getCmd(), response.getData()));
                } else {
                    emitter.onError(new AvlException(response.getMsg()));
                }
            })
            .groupBy(pair -> pair.first)
            .subscribe(new DefaultObserver<GroupedObservable<String, Pair<String, Object>>>() {
                @Override
                public void onSuccess(GroupedObservable<String, Pair<String, Object>> stringPairGroupedObservable) {
                    final String key = stringPairGroupedObservable.getKey();
                    switch (key) {
                        case Api.WsInterface.HEART_BEAT:
                            // 心跳响应
                            break;
                        case Api.WsInterface.WS_INIT:
                        case Api.WsInterface.WHITE_LIST:
                        case Api.WsInterface.AUTH:
                            // 处理配置消息
                            handleConfigMessage(stringPairGroupedObservable);
                            break;
                        default:
                            break;
                    }
                }
            });
        }

        /**
         * 处理配置消息
         */
        private void handleConfigMessage(GroupedObservable<String, Pair<String, Object>> observable) {
            observable
                .map((Function<Pair<String, Object>, ConfigModel>) stringStringPair -> {
                    JsonObject jsonObject = null;
                    ConfigModel configModel = new ConfigModel();
                    try {
                        jsonObject = GsonUtils.fromJson(GsonUtils.toJson(stringStringPair.second), JsonObject.class);
                        // 处理配置数据...
                        return configModel;
                    } catch (Exception e) {
                        Timber.e(e, "解析配置消息失败");
                        throw new RuntimeException("配置解析失败", e);
                    }
                })
                .flatMapCompletable(configModel -> {
                    return Repository.getInstance()
                            .saveConfig(configModel)
                            .doOnComplete(() -> {
                                DeviceConfigHelper.getInstance().process(configModel);
                            });
                })
                .subscribe(() -> {
                            Timber.i("配置保存成功");
                        },
                        Timber::e);
        }

        /**
         * 发送设备信息
         */
        private void sendDeviceInfo() {
            final DeviceModel deviceModel = new DeviceModel();
            deviceModel.setDeviceCode(DevInterfaceManager.getInstance().getSerialNumber());
            deviceModel.setMac(DeviceUtils.getMacAddress());
            final BaseRequest<DeviceModel> baseRequest = new BaseRequest<>(getUUID(), Api.WsInterface.WS_INIT);
            baseRequest.setData(deviceModel);
            send(baseRequest).subscribeOn(Schedulers.io()).subscribe();
        }
    }
}
