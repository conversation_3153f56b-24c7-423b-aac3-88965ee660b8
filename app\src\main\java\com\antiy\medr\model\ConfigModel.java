package com.antiy.medr.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.lang.reflect.Field;
import java.util.List;

import timber.log.Timber;

/**
 * Created on 2022/4/2.
 *
 * <AUTHOR>
 */
public class ConfigModel implements Parcelable {
    public static final Creator<ConfigModel> CREATOR = new Creator<ConfigModel>() {
        @Override
        public ConfigModel createFromParcel(Parcel source) {
            return new ConfigModel(source);
        }

        @Override
        public ConfigModel[] newArray(int size) {
            return new ConfigModel[size];
        }
    };
    //所有授权状态，默认是0，允许为1，不允许为2
    private StatusEnum lockStatus;
    private StatusEnum dataStatus;
    private StatusEnum sdAuth;
    private StatusEnum usbAuth;
    private StatusEnum wifiAuth;
    private StatusEnum bluetoothAuth;
    private List<String> usbWl;
    private List<String> wifiWl;
    @SerializedName("bluetooth")
    private List<String> bluetoothWl;
    private List<IptablesModel> ipWl;


    public ConfigModel() {
    }

    protected ConfigModel(Parcel in) {
        int tmpLockStatus = in.readInt();
        this.lockStatus = tmpLockStatus == -1 ? null : StatusEnum.values()[tmpLockStatus];
        int tmpDataStatus = in.readInt();
        this.dataStatus = tmpDataStatus == -1 ? null : StatusEnum.values()[tmpDataStatus];
        int tmpSdAuth = in.readInt();
        this.sdAuth = tmpSdAuth == -1 ? null : StatusEnum.values()[tmpSdAuth];
        int tmpUsbAuth = in.readInt();
        this.usbAuth = tmpUsbAuth == -1 ? null : StatusEnum.values()[tmpUsbAuth];
        int tmpWifiAuth = in.readInt();
        this.wifiAuth = tmpWifiAuth == -1 ? null : StatusEnum.values()[tmpWifiAuth];
        int tmpBluetoothAuth = in.readInt();
        this.bluetoothAuth = tmpBluetoothAuth == -1 ? null : StatusEnum.values()[tmpBluetoothAuth];
        this.usbWl = in.createStringArrayList();
        this.wifiWl = in.createStringArrayList();
        this.bluetoothWl = in.createStringArrayList();
        this.ipWl = in.createTypedArrayList(IptablesModel.CREATOR);
    }

    /**
     * 反射替换属性
     *
     * @param target
     */
    public void compareAndSwap(ConfigModel target) {
        final Field[] fields = target.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                final Object value = field.get(target);
                if (value != null) {
                    final Field declaredField = this.getClass().getDeclaredField(field.getName());
                    declaredField.setAccessible(true);
                    declaredField.set(this, value);
                }
            } catch (Exception e) {
                Timber.e(e);
            }
        }
    }

    public StatusEnum getWifiAuth() {
        return wifiAuth;
    }

    public void setWifiAuth(StatusEnum wifiAuth) {
        this.wifiAuth = wifiAuth;
    }

    public StatusEnum getLockStatus() {
        return lockStatus;
    }

    public void setLockStatus(StatusEnum lockStatusEnum) {
        this.lockStatus = lockStatusEnum;
    }

    public StatusEnum getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(StatusEnum dataStatusEnum) {
        this.dataStatus = dataStatusEnum;
    }

    public StatusEnum getSdAuth() {
        return sdAuth;
    }

    public void setSdAuth(StatusEnum sdAuth) {
        this.sdAuth = sdAuth;
    }

    public StatusEnum getUsbAuth() {
        return usbAuth;
    }

    public void setUsbAuth(StatusEnum usbAuth) {
        this.usbAuth = usbAuth;
    }

    public StatusEnum getBluetoothAuth() {
        return bluetoothAuth;
    }

    public void setBluetoothAuth(StatusEnum bluetoothAuth) {
        this.bluetoothAuth = bluetoothAuth;
    }

    public List<String> getUsbWl() {
        return usbWl;
    }

    public void setUsbWl(List<String> usbWl) {
        this.usbWl = usbWl;
    }

    public List<String> getWifiWl() {
        return wifiWl;
    }

    public void setWifiWl(List<String> wifiWl) {
        this.wifiWl = wifiWl;
    }

    public List<String> getBluetoothWl() {
        return bluetoothWl;
    }

    public void setBluetoothWl(List<String> bluetoothWl) {
        this.bluetoothWl = bluetoothWl;
    }

    public List<IptablesModel> getIpWl() {
        return ipWl;
    }

    public void setIpWl(List<IptablesModel> ipWl) {
        this.ipWl = ipWl;
    }

    @Override
    public String toString() {
        return "ConfigModel{" +
                "lockStatus=" + lockStatus +
                ", dataStatus=" + dataStatus +
                ", sdAuth=" + sdAuth +
                ", usbAuth=" + usbAuth +
                ", wifiAuth=" + wifiAuth +
                ", bluetoothAuth=" + bluetoothAuth +
                ", usbWl=" + usbWl +
                ", wifiWl=" + wifiWl +
                ", bluetoothWl=" + bluetoothWl +
                ", ipWl=" + ipWl +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.lockStatus == null ? -1 : this.lockStatus.ordinal());
        dest.writeInt(this.dataStatus == null ? -1 : this.dataStatus.ordinal());
        dest.writeInt(this.sdAuth == null ? -1 : this.sdAuth.ordinal());
        dest.writeInt(this.usbAuth == null ? -1 : this.usbAuth.ordinal());
        dest.writeInt(this.wifiAuth == null ? -1 : this.wifiAuth.ordinal());
        dest.writeInt(this.bluetoothAuth == null ? -1 : this.bluetoothAuth.ordinal());
        dest.writeStringList(this.usbWl);
        dest.writeStringList(this.wifiWl);
        dest.writeStringList(this.bluetoothWl);
        dest.writeTypedList(this.ipWl);
    }

    public void readFromParcel(Parcel source) {
        int tmpLockStatus = source.readInt();
        this.lockStatus = tmpLockStatus == -1 ? null : StatusEnum.values()[tmpLockStatus];
        int tmpDataStatus = source.readInt();
        this.dataStatus = tmpDataStatus == -1 ? null : StatusEnum.values()[tmpDataStatus];
        int tmpSdAuth = source.readInt();
        this.sdAuth = tmpSdAuth == -1 ? null : StatusEnum.values()[tmpSdAuth];
        int tmpUsbAuth = source.readInt();
        this.usbAuth = tmpUsbAuth == -1 ? null : StatusEnum.values()[tmpUsbAuth];
        int tmpWifiAuth = source.readInt();
        this.wifiAuth = tmpWifiAuth == -1 ? null : StatusEnum.values()[tmpWifiAuth];
        int tmpBluetoothAuth = source.readInt();
        this.bluetoothAuth = tmpBluetoothAuth == -1 ? null : StatusEnum.values()[tmpBluetoothAuth];
        this.usbWl = source.createStringArrayList();
        this.wifiWl = source.createStringArrayList();
        this.bluetoothWl = source.createStringArrayList();
        this.ipWl = source.createTypedArrayList(IptablesModel.CREATOR);
    }
}
