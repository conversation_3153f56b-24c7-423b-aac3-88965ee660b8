package com.antiy.medr.device.controller;

import android.content.Intent;
import android.os.Build;

import com.antiy.medr.data.source.Repository;
import com.antiy.medr.device.DevInterfaceManager;
import com.antiy.medr.device.LockScreenService;
import com.antiy.medr.model.EventEnum;
import com.antiy.medr.model.EventLog;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.TimeUtils;
import com.blankj.utilcode.util.Utils;

import java.util.List;

import ga.mdm.PolicyManager;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * Created on 2022/4/7.
 *
 * <AUTHOR>
 */
public class LockController implements IController<String>{

    public static boolean islock=false;
    @Override
    public void enable() {
//        DevInterfaceManager.getInstance().lockDevice();

        DevInterfaceManager.getInstance().setStatusBarNotificationDisabled(true);
        Repository.getInstance()
                .eventLog(new EventLog(EventEnum.LOCK, "远程锁定","远程锁定", TimeUtils.getNowString()))
                .subscribeOn(Schedulers.io())
                .subscribe();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Utils.getApp().startForegroundService(new Intent(Utils.getApp(), LockScreenService.class));
        } else {
            ServiceUtils.startService(LockScreenService.class);
        }
        islock=true;
    }

    @Override
    public void disable() {
//        DevInterfaceManager.getInstance().unlockDevice();
//        DevInterfaceManager.getInstance().setStatusBarExpandPanelDisabled(false);
        DevInterfaceManager.getInstance().setStatusBarNotificationDisabled(false);
        Repository.getInstance()
                .eventLog(new EventLog(EventEnum.LOCK, "远程解锁","远程解锁", TimeUtils.getNowString()))
                .subscribeOn(Schedulers.io())
                .subscribe();
        ServiceUtils.stopService(LockScreenService.class);
        islock=false;
    }

    @Override
    public boolean setWhiteList(List<String> whiteList) {
        return true;
    }

    public static boolean isLock(){
        return islock;
    }
}
