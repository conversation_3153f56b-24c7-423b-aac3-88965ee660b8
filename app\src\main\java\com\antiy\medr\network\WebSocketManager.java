package com.antiy.medr.network;

import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.util.Pair;

import com.antiy.medr.base.AvlException;
import com.antiy.medr.base.DefaultObserver;
import com.antiy.medr.data.source.Repository;
import com.antiy.medr.device.DevInterfaceManager;
import com.antiy.medr.device.DeviceConfigHelper;
import com.antiy.medr.model.BaseRequest;
import com.antiy.medr.model.BaseResponse;
import com.antiy.medr.model.ConfigModel;
import com.antiy.medr.model.DeviceModel;
import com.antiy.medr.model.EventLog;
import com.antiy.medr.model.IptablesModel;
import com.antiy.medr.model.StatusEnum;
import com.antiy.medr.util.CrashHandler;
import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.core.ObservableSource;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.functions.Function;
import io.reactivex.rxjava3.observables.GroupedObservable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okhttp3.logging.HttpLoggingInterceptor;
import timber.log.Timber;

/**
 * Created on 2022/4/1.
 *
 * <AUTHOR>
 */
public class WebSocketManager {

    private final Handler mHandler;
    private final Object lock = new Object();
    private volatile boolean connected = false;
    private volatile String mUrl;
    private WebSocket mWebSocket;

    private WebSocketManager() {
        final HandlerThread handlerThread = new HandlerThread("websocket looper thread");
        handlerThread.start();
        mHandler = new Handler(handlerThread.getLooper());
    }

    public static WebSocketManager getInstance() {
        return WebSocketHolder.INSTANCE;
    }

    public static String getUUID() {
        return DevInterfaceManager.getInstance().getSerialNumber();
    }

    public void connect(String url) {
        //已连接则先关闭上一个链接
        synchronized (lock) {
            mHandler.removeCallbacksAndMessages(null);
            if (connected && mWebSocket != null) {
                mWebSocket.cancel();
            }
            mUrl = url;
            HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor(new HttpLogger());
            interceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
            OkHttpClient client = new OkHttpClient.Builder()
                    .pingInterval(10, TimeUnit.SECONDS)
                    .addNetworkInterceptor(interceptor)
                    .build();
            Request request = new Request.Builder()
                    .url(url)
                    .build();
            CommandListener commandListener = new CommandListener();
            client.newWebSocket(request, commandListener);
        }
    }

    public Observable<Boolean> send(String text) {
        return Observable.defer(() -> Observable.create(emitter -> {
            synchronized (lock) {
                if (mWebSocket == null) {
                    emitter.onError(new AvlException("webSocket not connected!"));
                } else {
                    emitter.onNext(mWebSocket.send(text));
                    Timber.i(text);
                    emitter.onComplete();
                }
            }
        }));
    }

    /**
     * 发送消息
     *
     * @param request
     * @param <T>
     * @return
     */
    public <T> Observable<Boolean> send(BaseRequest<T> request) {
        return Observable.defer(() -> Observable.just(GsonUtils.toJson(request))
                .flatMap(WebSocketManager.this::send));
    }

    private static class WebSocketHolder {
        private static final WebSocketManager INSTANCE = new WebSocketManager();
    }

    private class CommandListener extends WebSocketListener {

        public static final int RECONNECT_INTERVAL = 5 * 1000;

        @Override
        public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
            super.onMessage(webSocket, text);
            Timber.i("message:%s", text);
            //ToastUtils.showLong(text);
            mHandler.post(() -> operate(text));
//            operate(text);
        }

        @Override
        public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
            super.onOpen(webSocket, response);
            Timber.i("开启连接");
            ToastUtils.showShort("与服务端连接成功");
            connected = true;
            synchronized (lock) {
                mWebSocket = webSocket;
            }
            sendDeviceInfo();
            mHandler.removeCallbacksAndMessages(null);
//            uploadLocalLogs();
        }

        @Override
        public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, @Nullable Response response) {
            super.onFailure(webSocket, t, response);
            Timber.e("连接失败 %s", t.toString());
            try {
                //应用未配置，使用离线配置
                if (DeviceConfigHelper.FIRST_PROCESS) {
                    CrashHandler.addlog("FIRST_PROCESS:"+DeviceConfigHelper.FIRST_PROCESS);
                    DeviceConfigHelper.FIRST_PROCESS=false;

                    ConfigModel  configModel=Repository.getInstance().getConfig().blockingFirst();
                    CrashHandler.addlog("连接超时，开始使用缓存配置");
                    CrashHandler.addlog(configModel.toString());
                    DeviceConfigHelper.getInstance().process(configModel);
                }
            }catch (Exception e){
                e.printStackTrace();
                CrashHandler.addlog("===调用离线配置出错===");
                CrashHandler.addlog(e.getMessage());
            }

            //ToastUtils.showShort("连接失败 %s", t.toString());
            connected = false;
            mHandler.postDelayed(() -> connect(mUrl), RECONNECT_INTERVAL);
        }

        @Override
        public void onClosed(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
            super.onClosed(webSocket, code, reason);
            Timber.e("连接关闭");
            connected = false;

        }

        /**
         * 处理服务端配置请求
         *
         * @param text
         */
        private void operate(String text) {
            CrashHandler.addlog(text);
            Observable.create((ObservableOnSubscribe<Pair<String, Object>>) emitter -> {
                final BaseResponse<Object> response = GsonUtils.fromJson(text, new TypeToken<BaseResponse<Object>>() {
                }.getType());
                if (response.getCode() == 0) {
                    emitter.onNext(Pair.create(response.getCmd(), response.getData()));
                } else {
                    emitter.onError(new AvlException(response.getMsg()));
                }

            })

                    .groupBy(pair -> pair.first)
                    .subscribe(new DefaultObserver<GroupedObservable<String, Pair<String, Object>>>() {
                        @Override
                        public void onSuccess(GroupedObservable<String, Pair<String, Object>> stringPairGroupedObservable) {
                            final String key = stringPairGroupedObservable.getKey();
                            switch (key) {
                                case Api.WsInterface.HEART_BEAT:
                                    //do nothing
                                    break;
                                case Api.WsInterface.WS_INIT:
                                case Api.WsInterface.WHITE_LIST:
                                case Api.WsInterface.AUTH:
                                    stringPairGroupedObservable
                                            .map((Function<Pair<String, Object>, ConfigModel>) stringStringPair -> {
                                                JsonObject jsonObject =null;
                                                ConfigModel configModel=new ConfigModel();
                                                try {
                                                    jsonObject = GsonUtils.fromJson(GsonUtils.toJson(stringStringPair.second),JsonObject.class);
                                                    //后端发送[]表示列表为空,这里替换[] 为 [nullsize]，否则[] 将被gson替换成null
                                                    if (jsonObject.get("usb_wl")!=null && jsonObject.get("usb_wl").isJsonArray()) {

                                                        if (jsonObject.get("usb_wl").getAsJsonArray().size()==0) {
                                                            configModel.setUsbWl(new ArrayList<>());
                                                        }else {
                                                            configModel.setUsbWl(GsonUtils.fromJson(jsonObject.get("usb_wl").getAsJsonArray().toString(), List.class));
                                                        }

                                                    }
                                                    if (jsonObject.get("wifi_wl")!=null && jsonObject.get("wifi_wl").isJsonArray() ) {
                                                        if (jsonObject.get("wifi_wl").getAsJsonArray().size()==0) {
                                                            configModel.setWifiWl(new ArrayList<>());
                                                        }else {
                                                            configModel.setWifiWl(GsonUtils.fromJson(jsonObject.get("wifi_wl").getAsJsonArray().toString(), List.class));
                                                        }

                                                    }
                                                    if (jsonObject.get("ip_wl")!=null && jsonObject.get("ip_wl").isJsonArray() ) {
                                                        if (jsonObject.get("ip_wl").getAsJsonArray().size()==0) {
                                                            configModel.setIpWl(new ArrayList<>());
                                                        }else {

                                                            List<IptablesModel> iptablesModels = new ArrayList<>();
                                                            for (JsonElement ip_wl : jsonObject.get("ip_wl").getAsJsonArray()) {
                                                                IptablesModel iptablesModel1 = GsonUtils.fromJson(ip_wl.toString(), IptablesModel.class);
                                                                iptablesModels.add(iptablesModel1);
                                                            }

                                                            configModel.setIpWl(iptablesModels);
                                                        }

                                                    }
                                                    if (jsonObject.get("bluetooth")!=null && jsonObject.get("bluetooth").isJsonArray() ) {
                                                        if (jsonObject.get("bluetooth").getAsJsonArray().size()==0) {
                                                            configModel.setBluetoothWl(new ArrayList<>());
                                                        }else {
                                                            configModel.setBluetoothWl(GsonUtils.fromJson(jsonObject.get("bluetooth").getAsJsonArray().toString(), List.class));
                                                        }
                                                    }

                                                    if (jsonObject.get("usb_wl")!=null && !jsonObject.get("usb_wl").isJsonArray()) {
                                                        configModel.setUsbWl(null);
                                                    }
                                                    if (jsonObject.get("wifi_wl")!=null && !jsonObject.get("wifi_wl").isJsonArray()) {
                                                        configModel.setWifiWl(null);
                                                    }
                                                    if (jsonObject.get("ip_wl")!=null && !jsonObject.get("ip_wl").isJsonArray()) {
                                                        configModel.setIpWl(null);
                                                    }
                                                    if (jsonObject.get("bluetooth")!=null && !jsonObject.get("bluetooth").isJsonArray()) {
                                                        configModel.setBluetoothWl(null);
                                                    }

                                                    if (jsonObject.get("data_status")!=null && !jsonObject.get("data_status").isJsonArray()) {
                                                        configModel.setDataStatus(StatusEnum.get(jsonObject.get("data_status").getAsInt()));
                                                    }
                                                    if (jsonObject.get("lock_status")!=null && !jsonObject.get("lock_status").isJsonArray()) {
                                                        configModel.setLockStatus(StatusEnum.get(jsonObject.get("lock_status").getAsInt()));
                                                    }
                                                    if (jsonObject.get("sd_auth")!=null && !jsonObject.get("sd_auth").isJsonArray()) {
                                                        configModel.setSdAuth(StatusEnum.get(jsonObject.get("sd_auth").getAsInt()));
                                                    }
                                                    if (jsonObject.get("usb_auth")!=null && !jsonObject.get("usb_auth").isJsonArray()) {
                                                        configModel.setUsbAuth(StatusEnum.get(jsonObject.get("usb_auth").getAsInt()));
                                                    }
                                                    if (jsonObject.get("bluetooth_auth")!=null && !jsonObject.get("bluetooth_auth").isJsonArray()) {
                                                        configModel.setBluetoothAuth(StatusEnum.get(jsonObject.get("bluetooth_auth").getAsInt()));
                                                    }
                                                    if (jsonObject.get("wifi_auth")!=null && !jsonObject.get("wifi_auth").isJsonArray()) {
                                                        configModel.setWifiAuth(StatusEnum.get(jsonObject.get("wifi_auth").getAsInt()));
                                                    }

                                                }catch (Exception e){
                                                    e.printStackTrace();
                                                    CrashHandler.addlog(Log.getStackTraceString(e));
                                                }

                                               // JsonObject jsonObject = GsonUtils.fromJson(GsonUtils.toJson(stringStringPair.second).replace("bluetooth","bluetooth_wl"), JsonObject.class);
                                               return configModel;
                                            })
                                            .flatMapCompletable(configModel -> {
                                              return  Repository.getInstance()
                                                        .saveConfig(configModel)
                                                        .doOnComplete(() ->{
                                                            DeviceConfigHelper.getInstance().process(configModel);
                                                           // new DeviceConfigHelper(configModel).process();
                                                        } );
                                            })
                                            .subscribe(() -> {
                                                        Timber.i("config 保存成功");
                                                    },
                                                    Timber::e);
                                    break;
                                default:
                                    break;
                            }
                        }
                    });
        }

        /**
         * 发送设备信息
         */
        private void sendDeviceInfo() {
            final DeviceModel deviceModel = new DeviceModel();
            deviceModel.setDeviceCode(DevInterfaceManager.getInstance().getSerialNumber());
            deviceModel.setMac(DeviceUtils.getMacAddress());
            final BaseRequest<DeviceModel> baseRequest = new BaseRequest<>(getUUID(), Api.WsInterface.WS_INIT);
            baseRequest.setData(deviceModel);
            send(baseRequest).subscribeOn(Schedulers.io()).subscribe();
        }

        /**
         * 上传本地数据库存储的违规事件
         */
        private void uploadLocalLogs() {
            Repository.getInstance()
                    //查询所有离线日志
                    .getAllEventLogs()
                    .subscribeOn(Schedulers.io())
                    .toObservable()
                    //铺平log列表
                    .flatMapIterable((Function<List<EventLog>, Iterable<EventLog>>) eventLogs -> eventLogs)
                    //一条一条上传日志
                    .flatMap((Function<EventLog, ObservableSource<Pair<Boolean, EventLog>>>) log ->
                            Repository.getInstance()
                                    .uploadLog(log)
                                    .map(aBoolean -> Pair.create(aBoolean, log)))
                    //删除本地数据库该日志
                    .flatMapCompletable(booleanEventLogPair -> {
                        if (Boolean.TRUE.equals(booleanEventLogPair.first)) {
                            return Repository.getInstance().deleteEventLog(booleanEventLogPair.second);
                        }
                        return Completable.complete();
                    })
                    .subscribe(() -> Timber.i("离线日志发送完毕"),
                            Timber::d);
        }
    }
}
