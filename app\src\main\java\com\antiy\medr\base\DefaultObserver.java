package com.antiy.medr.base;

import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import timber.log.Timber;

/**
 * Created on 2022/3/1.
 *
 * <AUTHOR>
 */
public abstract class DefaultObserver<T> implements Observer<T> {

    @Override
    public void onSubscribe(@NonNull Disposable d) {

    }

    @Override
    public void onNext(@NonNull T t) {
        onSuccess(t);
    }

    @Override
    public void onError(@NonNull Throwable e) {
        Timber.e(e);
        onFailure(e);
    }

    @Override
    public void onComplete() {

    }

    public abstract void onSuccess(T t);

    public void onFailure(Throwable throwable) {
        Timber.e(throwable);
    }
}
