package com.antiy.medr.data.source.local;

import com.antiy.medr.Constants;
import com.antiy.medr.data.db.AppDatabase;
import com.antiy.medr.data.source.IRepository;
import com.antiy.medr.model.ConfigModel;
import com.antiy.medr.model.EventLog;
import com.antiy.medr.network.Api;
import com.blankj.utilcode.util.CacheDiskUtils;

import java.util.List;

import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Single;

/**
 * Created on 2022/4/14.
 *
 * <AUTHOR>
 */
public class LocalRepository implements IRepository {
    @Override
    public Completable saveConfig(ConfigModel configModel) {
        return Completable.create(emitter -> {
            CacheDiskUtils.getInstance().put(Constants.Cache.CONFIG, configModel);
            emitter.onComplete();
        });
    }

    @Override
    public Observable<ConfigModel> getConfig() {
        return Observable.defer(() ->
                Observable.just(CacheDiskUtils.getInstance()
                        .getParcelable(Constants.Cache.CONFIG, ConfigModel.CREATOR, new ConfigModel())));
    }

    @Override
    public Observable<Boolean> uploadLog(EventLog log) {
        return Observable.create(emitter -> {
            AppDatabase.getInstance().eventLogDao().insertLogs(log).blockingAwait();
            emitter.onNext(false);
            emitter.onComplete();
        });
    }

    /**
     * 从磁盘缓存获取ws url,默认获取静态配置中保存的{@link Api}
     *
     * @return
     */
    @Override
    public Observable<String> getWsIp() {
        return Observable.defer(() ->
                Observable.just(CacheDiskUtils.getInstance().getString(Constants.Cache.WS_IP, Api.WS_IP)));
    }

    @Override
    public Completable saveWsIp(String url) {
        return Completable.create(emitter -> {
            CacheDiskUtils.getInstance().put(Constants.Cache.WS_IP, url);
            emitter.onComplete();
        });
    }

    @Override
    public Single<List<EventLog>> getAllEventLogs() {
        return AppDatabase.getInstance()
                .eventLogDao()
                .queryAll();
    }

    @Override
    public Completable deleteEventLog(EventLog... logs) {
        return AppDatabase.getInstance()
                .eventLogDao()
                .delete(logs);
    }

    @Override
    public Completable eventLog(EventLog log) {
        return null;
    }
}
