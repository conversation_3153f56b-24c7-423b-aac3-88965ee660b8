package com.antiy.medr.device.controller;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.widget.Toast;

import com.android.DevCtrl.DevInterface;
import com.antiy.medr.data.source.Repository;
import com.antiy.medr.device.DevInterfaceManager;
import com.antiy.medr.util.CrashHandler;
import com.blankj.utilcode.util.ArrayUtils;
import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.google.gson.JsonObject;

import org.json.JSONObject;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Set;

import io.reactivex.rxjava3.core.Maybe;
import timber.log.Timber;

/**
 * Created on 2022/4/7.
 *
 * <AUTHOR>
 */
public class BluetoothController implements IController<String> {
    /**
     * bluetoothPolicies
     * string[0]:功能模式，参见setBluetoothPolicies方法的mode参数。
     * string[1]至string[n]: 仅当mode=1时返回允许连接的特定蓝牙网络信息，参见setBluetoothPolicies方法的BluetoothInfoList参数
     */
    @Override
    public void enable() {//found different
        Maybe.fromObservable(Repository.getInstance()
                .getConfig())
                .subscribe(configModel -> {
                    final List<String> bluetoothWl = configModel.getBluetoothWl();
                    List<String> ans = transfer(bluetoothWl);
                    if (ans.size() == 0) {
                        ans.add("00:00:00:00:00:00");
                        ans = transfer(ans);
                    }

//                  关闭开启蓝牙会使上次配置的白名单失效，需要重新加载一次
                    boolean success = DevInterfaceManager.getInstance().setBluetoothPolicies(1, ans.toArray(new String[0]));
//                    boolean success = DevInterfaceManager.getInstance().setBluetoothPolicies(1, new String[]{});
//                    ToastUtils.showShort("蓝牙设置:" + (success ? "成功" : "失败"));

                });
    }

    @Override
    public void disable() {
        DevInterfaceManager.getInstance().setBluetoothPolicies(DISABLE, null);
    }

    /**
     * BluetoothInfoList：仅当mode=1时有效，数组中每一项为一个JSON格式字符串，格式如下：{"Mac":"00-11-22-33-44-55"}。
     *
     * @param whiteList
     */
    @Override
    public boolean setWhiteList(List<String> whiteList) {
        final DevInterface devInterface = DevInterfaceManager.getInstance();
        final String[] bluetoothPolicies = devInterface.getBluetoothPolicies();
        final String[] localWhiteArr = ArrayUtils.subArray(bluetoothPolicies, 1, bluetoothPolicies.length);
        final List<String> localList = new ArrayList<>(Arrays.asList(localWhiteArr));
        localList.removeAll(Collections.singleton(null));
        for (int i = 0; i < localList.size(); i++) {
            localList.set(i, localList.get(i).replace("-", ":"));
        }
        final List<String> ans = transfer(whiteList);
        if (ans.size()==0) {
        ans.add("00:00:00:00:00:00");
        }
        CrashHandler.addlog(Arrays.toString(whiteList.toArray(new String[0])));

/*        if (CollectionUtils.isEqualCollection(ans, localList)) {
            Timber.i("蓝牙白名单无变化");
            return true;
        }*/
        //ToastUtils.showLong(whiteList.toString());

/*        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (bluetoothAdapter!=null) {
            Set<BluetoothDevice> bondedDevices = bluetoothAdapter.getBondedDevices();
            for (BluetoothDevice bluetoothDevice : bondedDevices) {
                boolean isConnect =false;

                try {
                    isConnect= (boolean) bluetoothDevice.getClass().getMethod("isConnected").invoke(bluetoothDevice);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                }
                if(isConnect){
                    CrashHandler.addlog("正在连接");
                    //当前连接中的mac
                    for (String an : ans) {
                        String mac = an.trim().replace("-", ":");
                        //当前mac地址包含正在连接的地址
                        CrashHandler.addlog(mac);
                        if (mac.contains(bluetoothDevice.getAddress())) {
                            CrashHandler.addlog("close bluetooth");
                            DevInterfaceManager.getInstance().setBluetoothPolicies(DISABLE, null);
                        }
                    }
                }

            }
        }*/


        boolean result =  devInterface.setBluetoothPolicies(1, ans.toArray(new String[0]));
        CrashHandler.addlog("蓝牙白名单设置:" + (result ? "成功" : "失败"));
        return result;
    }

    private List<String> transfer(List<String> bluetooth) {
        if (bluetooth == null) {
            return new ArrayList<>();
        }
        final ArrayList<String> ans = new ArrayList<>();
        for (int i = 0; i < bluetooth.size(); i++) {
            if (bluetooth.get(i).trim().toLowerCase(Locale.ROOT).matches("^([0-9a-fA-F]{2})(([:|-][0-9a-fA-F]{2}){5})$")) {
                JsonObject jsonObject = new JsonObject();
                //jsonObject.addProperty("Mac", bluetooth.get(i).toLowerCase(Locale.ROOT));
                jsonObject.addProperty("Mac", bluetooth.get(i).trim().replace(":", "-"));
                String jsonStr = jsonObject.toString();
                ans.add(jsonStr);
            }
        }
        return ans;
    }

}
