# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with J<PERSON>, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /Users/<USER>/Library/Android/sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with <PERSON><PERSON>, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

-optimizationpasses 5

-dontusemixedcaseclassnames

-dontskipnonpubliclibraryclasses

-dontskipnonpubliclibraryclassmembers

-dontpreverify

-verbose
-printmapping priguardMapping.txt

-optimizations !code/simplification/artithmetic,!field/*,!class/merging/*

################common###############

#实体类不参与混淆
-keep class * implements android.os.Parcelable {
public static final android.os.Parcelable$Creator * ;
}
-keepnames class * implements java.io.Serializable
-keepattributes Signature
-keep class **.R$* {*                               ;}
-ignorewarnings
-keepclassmembers class **.R$* {
public static <fields>                              ;
}

-keepclasseswithmembernames class * { # 保持native方法不被混淆
native <methods>                      ;
}

-keepclassmembers enum * {                 # 使用enum类型时需要注意避免以下两个方法混淆，因为enum类的特殊性，以下两个方法会被反射调用，
public static **[] values()                ;
public static ** valueOf(java.lang.String) ;
}

# ViewBinding
-keep class * implements androidx.viewbinding.ViewBinding {
*                                                           ;
}

################support###############
-keep class android.support.** { *     ; }
-keep interface android.support.** { * ; }
-dontwarn android.support.**

################retrofit###############
-dontwarn retrofit2.**
-keep class retrofit2.** { * ; }
-keepattributes Signature
-keepattributes Exceptions

################butterknife###############
-keep class butterknife.** { *        ; }
-dontwarn butterknife.internal.**
-keep class **$$ViewBinder { *        ; }
-keepclasseswithmembernames class * {
@butterknife.* <fields>               ;
}
-keepclasseswithmembernames class * {
@butterknife.* <methods>              ;
}

################gson###############
-keepattributes Signature
-keepattributes *Annotation*
-keep class sun.misc.Unsafe { *                             ; }
-keep class com.google.gson.stream.** { *                   ; }
# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * implements com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Prevent R8 from leaving Data mMultiTypeEntity members always null
-keepclassmembers,allowobfuscation class * {
@com.google.gson.annotations.SerializedName <fields> ;
}
# Application classes that will be serialized/deserialized over Gson
# 將javabean添加忽略避免gson實例化失敗
-keep class com.antiy.medr.model.** { *              ; }
-keep class com.antiy.ccs.network.model.** { *       ; }

################glide###############
-keep public class * implements com.bumptech.glide.module.AppGlideModule
-keep public class * implements com.bumptech.glide.module.LibraryGlideModule
-keep class com.bumptech.glide.** { *                                            ; }
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
**[] $VALUES                                                                     ;
public *                                                                         ;
}

################okhttp###############
-keepattributes Signature
-keepattributes *Annotation*
-keep class com.squareup.okhttp.** { *     ; }
-keep interface com.squareup.okhttp.** { * ; }
-keep class okhttp3.** { *                 ; }
-keep interface okhttp3.** { *             ; }
-dontwarn com.squareup.okhttp.**

################androidEventBus###############
-keep class org.simple.** { *             ; }
-keep interface org.simple.** { *         ; }
-keepclassmembers class * {
@org.simple.eventbus.Subscriber <methods> ;
}
-keepattributes *Annotation*

################EventBus###############
-keepclassmembers class * {
@org.greenrobot.eventbus.Subscribe <methods>      ;
}
-keep class org.greenrobot.eventbus.EventBus { *  ; }
-keep enum org.greenrobot.eventbus.ThreadMode { * ; }

-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
<init>(java.lang.Throwable)                                                            ;
}

################RxJava and RxAndroid###############
-dontwarn org.mockito.**
-dontwarn org.junit.**
-dontwarn org.robolectric.**

-keep class io.reactivex.** { *     ; }
-keep interface io.reactivex.** { * ; }

-keepattributes Signature
-keepattributes *Annotation*
-keep class com.squareup.okhttp.** { *     ; }
-dontwarn okio.**
-keep interface com.squareup.okhttp.** { * ; }
-dontwarn com.squareup.okhttp.**

-dontwarn io.reactivex.**
-dontwarn retrofit.**
-keep class retrofit.** { *       ; }
-keepclasseswithmembers class * {
@retrofit.http.* <methods>        ;
}

-keep class sun.misc.Unsafe { * ; }

-dontwarn java.lang.invoke.*

-keep class io.reactivex.schedulers.Schedulers {
public static <methods>                                                                    ;
}
-keep class io.reactivex.schedulers.ImmediateScheduler {
public <methods>                                                                           ;
}
-keep class io.reactivex.schedulers.TestScheduler {
public <methods>                                                                           ;
}
-keep class io.reactivex.schedulers.Schedulers {
public static ** test()                                                                    ;
}
-keepclassmembers class io.reactivex.internal.util.unsafe.*ArrayQueue*Field* {
long producerIndex                                                                         ;
long consumerIndex                                                                         ;
}
-keepclassmembers class io.reactivex.internal.util.unsafe.BaseLinkedQueueProducerNodeRef {
long producerNode                                                                          ;
long consumerNode                                                                          ;
}

-keepclassmembers class io.reactivex.internal.util.unsafe.BaseLinkedQueueProducerNodeRef {
io.reactivex.internal.util.atomic.LinkedQueueNode producerNode                             ;
}
-keepclassmembers class io.reactivex.internal.util.unsafe.BaseLinkedQueueConsumerNodeRef {
io.reactivex.internal.util.atomic.LinkedQueueNode consumerNode                             ;
}

-dontwarn io.reactivex.internal.util.unsafe.**
-dontwarn java.util.concurrent.Flow*

################espresso###############
-keep class android.support.test.espresso.** { *     ; }
-keep interface android.support.test.espresso.** { * ; }

################annotation###############
-keep class android.support.annotation.** { *     ; }
-keep interface android.support.annotation.** { * ; }

################RxPermissions#################
-keep class com.tbruyelle.rxpermissions2.** { *     ; }
-keep interface com.tbruyelle.rxpermissions2.** { * ; }

################Timber#################
-dontwarn org.jetbrains.annotations.**

################Canary#################
-dontwarn com.squareup.haha.guava.**
-dontwarn com.squareup.haha.perflib.**
-dontwarn com.squareup.haha.trove.**
-dontwarn com.squareup.leakcanary.**
-keep class com.squareup.haha.** { *       ; }
-keep class com.squareup.leakcanary.** { * ; }

# Marshmallow removed Notification.setLatestEventInfo()
-dontwarn android.app.Notification

#Progressmanager
-keep class me.jessyan.progressmanager.** { *     ; }
-keep interface me.jessyan.progressmanager.** { * ; }

#avlrisk
-keep class com.avl.** { * ; }

-keep class com.avl.engine.** {* ;}

-dontwarn com.google.**
-keep class com.google.gson.** {* ;}

#==================protobuf======================
-dontwarn com.google.**
-keep class com.google.protobuf.** {* ;}
#==================================================================

-keep class * implements com.antiy.common_lib.delegate.AppLifecycle

-dontwarn android.content.pm.**
