package com.antiy.medr.plugin;

import android.text.TextUtils;
import android.util.Pair;

import org.xplugin.core.ctx.Module;
import org.xplugin.core.install.Installer;
import org.xutils.common.Callback;

import java.io.File;
import java.util.LinkedList;
import java.util.Set;
import java.util.concurrent.CountDownLatch;

public class XPluginHelper extends Thread {
    private static CountDownLatch mCountDownLatch = null;

    private LinkedList<Pair<String, String>> mQueue = null;
    private XPluginEvent mPluginEvent = null;

    private XPluginHelper(LinkedList<Pair<String, String>> taskSet, XPluginEvent event) {
        super();
        mQueue = taskSet;
        mPluginEvent = event;
    }

    @Override
    public void run() {
        if (mQueue == null || mQueue.isEmpty()) {
            if (mPluginEvent != null) {
                mPluginEvent.onLoadComplete();
            }
            return;
        }
        try {
            if (mCountDownLatch != null) {
                mCountDownLatch.await();
            }
        } catch(InterruptedException e) {
            e.printStackTrace();
        }

        Callback.CommonCallback<Module> callback = new Callback.CommonCallback<Module>() {
            @Override
            public void onSuccess(Module result) {

            }

            @Override
            public void onError(Throwable ex, boolean isOnCallback) {

            }

            @Override
            public void onCancelled(CancelledException cex) {

            }

            @Override
            public void onFinished() {
                if (mQueue.isEmpty()) {
                    if (mCountDownLatch != null) {
                        mCountDownLatch.countDown();
                    }
                    if (mPluginEvent != null) {
                        mPluginEvent.onLoadComplete();
                    }
                } else {
                    Pair<String, String> p = mQueue.poll();
                    installModule(p.first, p.second, this);
                }
            }
        };
        mCountDownLatch = new CountDownLatch(1);
        Pair<String, String> f = mQueue.poll();
        installModule(f.first, f.second, callback);
    }

    public void excuteInstall() {
        start();
    }

    private void installModule(final String filePath, final String pkg, final Callback.CommonCallback<Module> callback) {
        final Set<String> installedModules = Installer.getInstalledModules();
        if (!installedModules.contains(pkg)) {
            //未安装
            Installer.installModule(new File(filePath), callback);
        } else {
            //已安装未加载
            Installer.loadModule(pkg, callback);
        }
    }

    public static interface XPluginEvent {
        void onLoadComplete();
    }

    public static class Builder {
        private LinkedList<Pair<String, String>> mQueue = new LinkedList<>();
        private XPluginEvent mPluginEvent = null;

        public Builder() {

        }

        public Builder addPlugin(String filePath, String pkg) {
            if (TextUtils.isEmpty(filePath) || TextUtils.isEmpty(pkg)) {
                return this;
            }
            if (!new File(filePath).exists()) {
                return this;
            }

            mQueue.add(new Pair<>(filePath, pkg));
            return this;
        }

        public Builder setPluginEvent(XPluginEvent event) {
            mPluginEvent = event;
            return this;
        }

        public XPluginHelper build() {
            return new XPluginHelper(mQueue, mPluginEvent);
        }
    }
}
