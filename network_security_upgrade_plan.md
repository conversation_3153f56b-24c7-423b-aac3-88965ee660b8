# Android项目网络通信安全升级技术实施文档

## 项目概述

**项目名称**: antiy-medr 设备管控应用  
**升级目标**: 将所有网络通信从明文传输升级为加密传输  
**涉及协议**: HTTP → HTTPS, WebSocket (ws://) → Secure WebSocket (wss://)  

## 1. 现状分析

### 1.1 当前网络通信架构

基于之前的代码分析，项目当前的网络通信包括：

1. **WebSocket连接**: 使用明文ws://协议
2. **HTTP请求**: 可能存在的HTTP API调用
3. **设备管控通信**: 通过WebSocket进行实时配置下发

### 1.2 需要修改的核心文件

```
app/src/main/java/com/antiy/medr/network/
├── Api.java                    # API接口定义
├── WebSocketManager.java       # WebSocket管理器
└── HttpLogger.java            # HTTP日志记录器

app/src/main/java/com/antiy/medr/
├── app/App.java               # 应用初始化
├── ui/MainActivity.java       # 主界面WebSocket连接
└── device/DeviceBroadcastReceiver.java  # 广播接收器
```

## 2. 技术实施方案

### 2.1 WebSocket安全升级方案

#### 2.1.1 修改API接口定义

**文件**: `app/src/main/java/com/antiy/medr/network/Api.java`

```java
public final class Api {
    // 原有配置
    public static final String WS_IP = "************";
    public static final String PORT = "2222";
    public static final String SECURE_PORT = "2223";  // 新增安全端口
    
    // 新增SSL配置
    public static final boolean USE_SSL = true;
    public static final String SSL_PROTOCOL = "TLS";
    
    private Api() {}
    
    // 修改URL构建方法
    public static String getWsLinkFromIp(String ipAddress) {
        if (USE_SSL) {
            return "wss://" + ipAddress + ":" + SECURE_PORT + "/ws_init";
        } else {
            return "ws://" + ipAddress + ":" + PORT + "/ws_init";
        }
    }
    
    // 新增HTTPS URL构建方法
    public static String getHttpsLinkFromIp(String ipAddress, String endpoint) {
        if (USE_SSL) {
            return "https://" + ipAddress + ":" + SECURE_PORT + endpoint;
        } else {
            return "http://" + ipAddress + ":" + PORT + endpoint;
        }
    }
}
```

#### 2.1.2 升级WebSocketManager

**文件**: `app/src/main/java/com/antiy/medr/network/WebSocketManager.java`

```java
public class WebSocketManager {
    // 新增SSL配置相关字段
    private SSLContext sslContext;
    private X509TrustManager trustManager;
    private HostnameVerifier hostnameVerifier;
    
    // 修改connect方法
    public void connect(String url) {
        synchronized (lock) {
            mHandler.removeCallbacksAndMessages(null);
            if (connected && mWebSocket != null) {
                mWebSocket.cancel();
            }
            mUrl = url;
            
            HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor(new HttpLogger());
            interceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
            
            OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder()
                    .pingInterval(10, TimeUnit.SECONDS)
                    .addNetworkInterceptor(interceptor);
            
            // 配置SSL
            if (url.startsWith("wss://")) {
                configureSsl(clientBuilder);
            }
            
            OkHttpClient client = clientBuilder.build();
            Request request = new Request.Builder().url(url).build();
            CommandListener commandListener = new CommandListener();
            client.newWebSocket(request, commandListener);
        }
    }
    
    // 新增SSL配置方法
    private void configureSsl(OkHttpClient.Builder builder) {
        try {
            // 创建信任管理器
            trustManager = createTrustManager();
            
            // 创建SSL上下文
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{trustManager}, new SecureRandom());
            
            // 配置SSL套接字工厂
            builder.sslSocketFactory(sslContext.getSocketFactory(), trustManager);
            
            // 配置主机名验证器
            hostnameVerifier = createHostnameVerifier();
            builder.hostnameVerifier(hostnameVerifier);
            
        } catch (Exception e) {
            Timber.e(e, "SSL配置失败");
            CrashHandler.addlog("SSL配置失败: " + e.getMessage());
        }
    }
}
```

### 2.2 SSL/TLS证书配置方案

#### 2.2.1 证书管理策略

```java
// 新增证书管理类
public class CertificateManager {
    private static final String CERTIFICATE_ALIAS = "antiy_server_cert";
    private static CertificateManager instance;
    
    public static CertificateManager getInstance() {
        if (instance == null) {
            synchronized (CertificateManager.class) {
                if (instance == null) {
                    instance = new CertificateManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 创建自定义信任管理器
     * 支持自签名证书和CA证书
     */
    public X509TrustManager createTrustManager(Context context) {
        try {
            // 加载应用内置证书
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            InputStream caInput = context.getAssets().open("server_cert.crt");
            Certificate ca = cf.generateCertificate(caInput);
            caInput.close();
            
            // 创建KeyStore
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null, null);
            keyStore.setCertificateEntry(CERTIFICATE_ALIAS, ca);
            
            // 创建TrustManager
            TrustManagerFactory tmf = TrustManagerFactory.getInstance(
                TrustManagerFactory.getDefaultAlgorithm());
            tmf.init(keyStore);
            
            return (X509TrustManager) tmf.getTrustManagers()[0];
            
        } catch (Exception e) {
            Timber.e(e, "创建信任管理器失败");
            // 返回默认信任管理器
            return getDefaultTrustManager();
        }
    }
    
    /**
     * 创建主机名验证器
     */
    public HostnameVerifier createHostnameVerifier() {
        return new HostnameVerifier() {
            @Override
            public boolean verify(String hostname, SSLSession session) {
                // 生产环境应该进行严格的主机名验证
                // 这里可以根据实际需求配置验证逻辑
                return isValidHostname(hostname);
            }
        };
    }
    
    private boolean isValidHostname(String hostname) {
        // 验证主机名是否在允许列表中
        String[] allowedHosts = {
            "************",  // 开发环境
            "your-server.com", // 生产环境
            "localhost"       // 测试环境
        };
        
        for (String allowedHost : allowedHosts) {
            if (hostname.equals(allowedHost)) {
                return true;
            }
        }
        return false;
    }
}
```

### 2.3 渐进式升级策略

#### 2.3.1 配置开关机制

```java
// 在Constants类中添加配置开关
public final class Constants {
    public static class Network {
        // SSL配置开关
        public static final boolean ENABLE_SSL = BuildConfig.DEBUG ? false : true;
        
        // 证书验证模式
        public static final String CERT_VALIDATION_MODE = "STRICT"; // STRICT, PERMISSIVE, DISABLED
        
        // 回退机制开关
        public static final boolean ENABLE_FALLBACK = true;
    }
}
```

#### 2.3.2 智能回退机制

```java
public class NetworkFallbackManager {
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private int sslRetryCount = 0;
    
    public void connectWithFallback(String baseUrl) {
        // 首先尝试SSL连接
        String secureUrl = convertToSecureUrl(baseUrl);
        
        connectWithRetry(secureUrl, new ConnectionCallback() {
            @Override
            public void onSuccess() {
                Timber.i("SSL连接成功");
                sslRetryCount = 0;
            }
            
            @Override
            public void onFailure(Throwable error) {
                Timber.w(error, "SSL连接失败，尝试回退");
                handleSslFailure(baseUrl, error);
            }
        });
    }
    
    private void handleSslFailure(String baseUrl, Throwable error) {
        sslRetryCount++;
        
        if (sslRetryCount < MAX_RETRY_ATTEMPTS) {
            // 重试SSL连接
            Timber.i("重试SSL连接，第%d次", sslRetryCount);
            connectWithFallback(baseUrl);
        } else if (Constants.Network.ENABLE_FALLBACK) {
            // 回退到非SSL连接
            Timber.w("SSL连接多次失败，回退到非SSL连接");
            String insecureUrl = convertToInsecureUrl(baseUrl);
            WebSocketManager.getInstance().connect(insecureUrl);
        } else {
            // 不允许回退，报告错误
            Timber.e("SSL连接失败且不允许回退");
            CrashHandler.addlog("SSL连接失败: " + error.getMessage());
        }
    }
}
```

## 3. 实施步骤

### 3.1 准备阶段

1. **证书准备**
   - 获取服务器SSL证书
   - 将证书文件放置到`app/src/main/assets/`目录
   - 配置服务器支持WSS协议

2. **依赖更新**
   ```gradle
   // 在app/build.gradle中确保OkHttp版本支持现代SSL
   implementation 'com.squareup.okhttp3:okhttp:4.12.0'
   implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
   ```

### 3.2 代码修改阶段

1. **第一步：创建证书管理类**
   - 创建`CertificateManager.java`
   - 实现证书加载和验证逻辑

2. **第二步：修改API配置**
   - 更新`Api.java`中的URL构建方法
   - 添加SSL配置常量

3. **第三步：升级WebSocketManager**
   - 添加SSL配置方法
   - 集成证书管理器
   - 实现回退机制

### 3.3 测试验证阶段

1. **单元测试**
2. **集成测试**
3. **安全测试**

## 4. 风险评估与应对

### 4.1 主要风险点

1. **证书验证失败**
2. **性能影响**
3. **兼容性问题**
4. **网络环境限制**

### 4.2 应对策略

1. **多重验证机制**
2. **性能监控**
3. **渐进式部署**
4. **完善的日志记录**

## 5. 详细代码实现

### 5.1 完整的WebSocketManager SSL配置

```java
// 在WebSocketManager中添加完整的SSL配置方法
private void configureSsl(OkHttpClient.Builder builder) {
    try {
        CertificateManager certManager = CertificateManager.getInstance();

        // 创建信任管理器
        X509TrustManager trustManager = certManager.createTrustManager(Utils.getApp());

        // 创建SSL上下文
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, new TrustManager[]{trustManager}, new SecureRandom());

        // 配置SSL套接字工厂
        builder.sslSocketFactory(sslContext.getSocketFactory(), trustManager);

        // 配置主机名验证器
        HostnameVerifier hostnameVerifier = certManager.createHostnameVerifier();
        builder.hostnameVerifier(hostnameVerifier);

        // 配置连接超时
        builder.connectTimeout(30, TimeUnit.SECONDS);
        builder.readTimeout(30, TimeUnit.SECONDS);
        builder.writeTimeout(30, TimeUnit.SECONDS);

        Timber.i("SSL配置完成");

    } catch (Exception e) {
        Timber.e(e, "SSL配置失败");
        CrashHandler.addlog("SSL配置失败: " + e.getMessage());

        // 根据配置决定是否抛出异常
        if (Constants.Network.CERT_VALIDATION_MODE.equals("STRICT")) {
            throw new RuntimeException("SSL配置失败，无法建立安全连接", e);
        }
    }
}

// 新增连接状态监控
private void monitorConnection(String url) {
    long startTime = System.currentTimeMillis();

    // 在CommandListener中添加监控逻辑
    private class CommandListener extends WebSocketListener {
        @Override
        public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
            super.onOpen(webSocket, response);
            long duration = System.currentTimeMillis() - startTime;

            Timber.i("WebSocket连接成功");
            SecurityLogger.logSslConnection(mUrl, true, duration);
            ToastUtils.showShort("与服务端连接成功");

            connected = true;
            synchronized (lock) {
                mWebSocket = webSocket;
            }
            sendDeviceInfo();
            mHandler.removeCallbacksAndMessages(null);
        }

        @Override
        public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, @Nullable Response response) {
            super.onFailure(webSocket, t, response);
            long duration = System.currentTimeMillis() - startTime;

            Timber.e("连接失败 %s", t.toString());
            SecurityLogger.logSslConnection(mUrl, false, duration);

            // SSL特定错误处理
            if (t instanceof SSLException) {
                handleSslError((SSLException) t);
            } else {
                handleGeneralConnectionError(t);
            }
        }
    }
}

// SSL错误处理
private void handleSslError(SSLException sslException) {
    String errorMessage = "SSL连接错误: " + sslException.getMessage();
    Timber.e(errorMessage);
    CrashHandler.addlog(errorMessage);

    // 根据错误类型决定处理策略
    if (sslException.getMessage().contains("certificate")) {
        // 证书相关错误
        handleCertificateError();
    } else if (sslException.getMessage().contains("handshake")) {
        // 握手错误
        handleHandshakeError();
    } else {
        // 其他SSL错误
        handleOtherSslError();
    }
}
```

### 5.2 证书管理完整实现

```java
public class CertificateManager {
    private static final String TAG = "CertificateManager";
    private static final String CERTIFICATE_FILE = "server_cert.crt";
    private static final String CERTIFICATE_ALIAS = "antiy_server_cert";

    private static volatile CertificateManager instance;
    private X509TrustManager defaultTrustManager;
    private X509TrustManager customTrustManager;

    private CertificateManager() {
        initializeDefaultTrustManager();
    }

    public static CertificateManager getInstance() {
        if (instance == null) {
            synchronized (CertificateManager.class) {
                if (instance == null) {
                    instance = new CertificateManager();
                }
            }
        }
        return instance;
    }

    private void initializeDefaultTrustManager() {
        try {
            TrustManagerFactory factory = TrustManagerFactory.getInstance(
                TrustManagerFactory.getDefaultAlgorithm());
            factory.init((KeyStore) null);

            for (TrustManager tm : factory.getTrustManagers()) {
                if (tm instanceof X509TrustManager) {
                    defaultTrustManager = (X509TrustManager) tm;
                    break;
                }
            }
        } catch (Exception e) {
            Timber.e(e, "初始化默认信任管理器失败");
        }
    }

    public X509TrustManager createTrustManager(Context context) {
        if (customTrustManager != null) {
            return customTrustManager;
        }

        try {
            // 根据验证模式选择不同的信任管理器
            switch (Constants.Network.CERT_VALIDATION_MODE) {
                case "STRICT":
                    customTrustManager = createStrictTrustManager(context);
                    break;
                case "PERMISSIVE":
                    customTrustManager = createPermissiveTrustManager(context);
                    break;
                case "DISABLED":
                    customTrustManager = createDisabledTrustManager();
                    break;
                default:
                    customTrustManager = createStrictTrustManager(context);
            }

            return customTrustManager;

        } catch (Exception e) {
            Timber.e(e, "创建自定义信任管理器失败，使用默认管理器");
            return defaultTrustManager;
        }
    }

    private X509TrustManager createStrictTrustManager(Context context) throws Exception {
        // 加载应用内置证书
        InputStream caInput = context.getAssets().open(CERTIFICATE_FILE);
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        Certificate ca = cf.generateCertificate(caInput);
        caInput.close();

        // 创建包含自定义证书的KeyStore
        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        keyStore.load(null, null);
        keyStore.setCertificateEntry(CERTIFICATE_ALIAS, ca);

        // 创建信任管理器
        TrustManagerFactory tmf = TrustManagerFactory.getInstance(
            TrustManagerFactory.getDefaultAlgorithm());
        tmf.init(keyStore);

        return (X509TrustManager) tmf.getTrustManagers()[0];
    }

    private X509TrustManager createPermissiveTrustManager(Context context) {
        return new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType)
                    throws CertificateException {
                // 客户端证书验证（通常不需要）
            }

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType)
                    throws CertificateException {
                try {
                    // 首先尝试默认验证
                    defaultTrustManager.checkServerTrusted(chain, authType);
                } catch (CertificateException e) {
                    // 默认验证失败，尝试自定义证书验证
                    Timber.w("默认证书验证失败，尝试自定义验证");
                    validateCustomCertificate(chain, authType);
                }
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return defaultTrustManager.getAcceptedIssuers();
            }

            private void validateCustomCertificate(X509Certificate[] chain, String authType)
                    throws CertificateException {
                if (chain == null || chain.length == 0) {
                    throw new CertificateException("证书链为空");
                }

                // 检查证书有效期
                X509Certificate cert = chain[0];
                cert.checkValidity();

                // 可以添加更多自定义验证逻辑
                Timber.i("自定义证书验证通过");
            }
        };
    }

    private X509TrustManager createDisabledTrustManager() {
        Timber.w("警告：证书验证已禁用，仅用于开发环境");
        return new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {}

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {}

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        };
    }

    public HostnameVerifier createHostnameVerifier() {
        return new HostnameVerifier() {
            @Override
            public boolean verify(String hostname, SSLSession session) {
                switch (Constants.Network.CERT_VALIDATION_MODE) {
                    case "STRICT":
                        return verifyHostnameStrict(hostname, session);
                    case "PERMISSIVE":
                        return verifyHostnamePermissive(hostname);
                    case "DISABLED":
                        return true;
                    default:
                        return verifyHostnameStrict(hostname, session);
                }
            }
        };
    }

    private boolean verifyHostnameStrict(String hostname, SSLSession session) {
        try {
            // 使用默认的主机名验证器
            return HttpsURLConnection.getDefaultHostnameVerifier().verify(hostname, session);
        } catch (Exception e) {
            Timber.e(e, "严格主机名验证失败");
            return false;
        }
    }

    private boolean verifyHostnamePermissive(String hostname) {
        // 验证主机名是否在允许列表中
        String[] allowedHosts = getAllowedHosts();

        for (String allowedHost : allowedHosts) {
            if (hostname.equals(allowedHost) || isWildcardMatch(hostname, allowedHost)) {
                return true;
            }
        }

        Timber.w("主机名不在允许列表中: %s", hostname);
        return false;
    }

    private String[] getAllowedHosts() {
        // 可以从配置文件或远程配置获取
        return new String[]{
            "************",
            "*.antiy.com",
            "localhost",
            "127.0.0.1"
        };
    }

    private boolean isWildcardMatch(String hostname, String pattern) {
        if (!pattern.startsWith("*.")) {
            return hostname.equals(pattern);
        }

        String domain = pattern.substring(2);
        return hostname.endsWith("." + domain) || hostname.equals(domain);
    }
}
```

### 5.3 配置管理和开关控制

```java
// 在Constants类中添加详细的网络配置
public final class Constants {
    public static class Network {
        // 基础SSL配置
        public static final boolean ENABLE_SSL = !BuildConfig.DEBUG;
        public static final String SSL_PROTOCOL = "TLS";
        public static final String[] SUPPORTED_PROTOCOLS = {"TLSv1.2", "TLSv1.3"};

        // 证书验证模式
        public static final String CERT_VALIDATION_STRICT = "STRICT";
        public static final String CERT_VALIDATION_PERMISSIVE = "PERMISSIVE";
        public static final String CERT_VALIDATION_DISABLED = "DISABLED";

        // 当前验证模式（可通过远程配置动态调整）
        public static String CERT_VALIDATION_MODE = BuildConfig.DEBUG ?
            CERT_VALIDATION_DISABLED : CERT_VALIDATION_STRICT;

        // 回退机制配置
        public static final boolean ENABLE_FALLBACK = true;
        public static final int MAX_SSL_RETRY_ATTEMPTS = 3;
        public static final long SSL_RETRY_DELAY_MS = 2000;

        // 超时配置
        public static final int SSL_CONNECT_TIMEOUT_SECONDS = 30;
        public static final int SSL_READ_TIMEOUT_SECONDS = 30;
        public static final int SSL_WRITE_TIMEOUT_SECONDS = 30;

        // 端口配置
        public static final String DEFAULT_PORT = "2222";
        public static final String SSL_PORT = "2223";
    }

    public static class Cache {
        public static final String CONFIG = "config";
        public static final String WS_IP = "ws_ip";
        public static final String SSL_CONFIG = "ssl_config";  // 新增SSL配置缓存
    }
}
```

### 5.4 安全日志和监控

```java
public class SecurityLogger {
    private static final String TAG = "SecurityLogger";
    private static final String LOG_FILE_PREFIX = "security_";

    public static void logSslConnection(String url, boolean success, long duration) {
        String logMessage = String.format(
            "SSL连接: URL=%s, 成功=%b, 耗时=%dms, 时间=%s",
            url, success, duration, getCurrentTimestamp()
        );

        Timber.i(logMessage);
        CrashHandler.addlog(logMessage);

        // 记录到专门的安全日志文件
        writeSecurityLog(logMessage);
    }

    public static void logCertificateInfo(X509Certificate cert) {
        try {
            String certInfo = String.format(
                "证书信息: 主题=%s, 颁发者=%s, 有效期=%s到%s",
                cert.getSubjectDN().getName(),
                cert.getIssuerDN().getName(),
                cert.getNotBefore(),
                cert.getNotAfter()
            );

            Timber.i(certInfo);
            writeSecurityLog(certInfo);

        } catch (Exception e) {
            Timber.e(e, "记录证书信息失败");
        }
    }

    public static void logSecurityEvent(String event, String details) {
        String logMessage = String.format(
            "安全事件: %s, 详情=%s, 时间=%s",
            event, details, getCurrentTimestamp()
        );

        Timber.w(logMessage);
        CrashHandler.addlog(logMessage);
        writeSecurityLog(logMessage);
    }

    private static void writeSecurityLog(String message) {
        try {
            if (CrashHandler.is_Debug) {
                DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String date = formatter.format(new Date());
                String fileName = LOG_FILE_PREFIX + date + ".log";

                if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
                    String path = "/sdcard/security_logs/";
                    File dir = new File(path);
                    if (!dir.exists()) {
                        dir.mkdirs();
                    }

                    FileOutputStream fos = new FileOutputStream(path + fileName, true);
                    fos.write(("\n" + message).getBytes());
                    fos.close();
                }
            }
        } catch (Exception e) {
            Timber.e(e, "写入安全日志失败");
        }
    }

    private static String getCurrentTimestamp() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
            .format(new Date());
    }
}
```

## 6. 测试验证方案

### 6.1 单元测试

```java
@RunWith(AndroidJUnit4.class)
public class CertificateManagerTest {

    @Test
    public void testCreateTrustManager() {
        Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        CertificateManager manager = CertificateManager.getInstance();

        X509TrustManager trustManager = manager.createTrustManager(context);
        assertNotNull("信任管理器不应为空", trustManager);
    }

    @Test
    public void testHostnameVerification() {
        CertificateManager manager = CertificateManager.getInstance();
        HostnameVerifier verifier = manager.createHostnameVerifier();

        // 测试有效主机名
        assertTrue("应该接受有效主机名", verifier.verify("************", null));

        // 测试无效主机名
        assertFalse("应该拒绝无效主机名", verifier.verify("malicious.com", null));
    }
}
```

### 6.2 集成测试

```java
@RunWith(AndroidJUnit4.class)
public class WebSocketSslTest {

    @Test
    public void testSslWebSocketConnection() {
        CountDownLatch latch = new CountDownLatch(1);
        AtomicBoolean connectionSuccess = new AtomicBoolean(false);

        // 模拟SSL WebSocket连接
        String testUrl = "wss://test-server.com:2223/ws_init";

        WebSocketManager.getInstance().connect(testUrl);

        // 等待连接结果
        try {
            latch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            fail("连接测试超时");
        }

        assertTrue("SSL WebSocket连接应该成功", connectionSuccess.get());
    }
}
```

### 6.3 安全测试清单

1. **证书验证测试**
   - [ ] 有效证书连接测试
   - [ ] 过期证书拒绝测试
   - [ ] 自签名证书处理测试
   - [ ] 证书链验证测试

2. **主机名验证测试**
   - [ ] 正确主机名验证
   - [ ] 错误主机名拒绝
   - [ ] 通配符证书测试

3. **协议安全测试**
   - [ ] TLS版本协商测试
   - [ ] 弱加密套件拒绝测试
   - [ ] 中间人攻击防护测试

4. **回退机制测试**
   - [ ] SSL失败回退测试
   - [ ] 重试机制测试
   - [ ] 配置开关测试

## 7. 部署和维护

### 7.1 分阶段部署策略

1. **第一阶段：开发环境验证**
   - 在开发环境启用SSL
   - 验证基本功能正常
   - 性能基准测试

2. **第二阶段：测试环境部署**
   - 完整功能测试
   - 安全测试验证
   - 压力测试

3. **第三阶段：生产环境灰度**
   - 小范围用户测试
   - 监控关键指标
   - 问题快速响应

4. **第四阶段：全量部署**
   - 全面启用SSL
   - 持续监控
   - 优化调整

### 7.2 监控指标

```java
public class NetworkMetrics {
    private static final String METRICS_TAG = "NetworkMetrics";

    // 连接成功率
    private static AtomicInteger sslConnectionAttempts = new AtomicInteger(0);
    private static AtomicInteger sslConnectionSuccesses = new AtomicInteger(0);

    // 连接时间统计
    private static List<Long> connectionTimes = new CopyOnWriteArrayList<>();

    public static void recordConnectionAttempt() {
        sslConnectionAttempts.incrementAndGet();
    }

    public static void recordConnectionSuccess(long duration) {
        sslConnectionSuccesses.incrementAndGet();
        connectionTimes.add(duration);

        // 保持最近100次记录
        if (connectionTimes.size() > 100) {
            connectionTimes.remove(0);
        }
    }

    public static double getSuccessRate() {
        int attempts = sslConnectionAttempts.get();
        int successes = sslConnectionSuccesses.get();
        return attempts > 0 ? (double) successes / attempts : 0.0;
    }

    public static double getAverageConnectionTime() {
        if (connectionTimes.isEmpty()) {
            return 0.0;
        }

        long sum = connectionTimes.stream().mapToLong(Long::longValue).sum();
        return (double) sum / connectionTimes.size();
    }

    public static void logMetrics() {
        String metricsLog = String.format(
            "SSL连接指标: 成功率=%.2f%%, 平均连接时间=%.2fms, 总尝试次数=%d",
            getSuccessRate() * 100,
            getAverageConnectionTime(),
            sslConnectionAttempts.get()
        );

        Timber.i(metricsLog);
        SecurityLogger.logSecurityEvent("METRICS", metricsLog);
    }
}
```

### 7.3 证书更新机制

```java
public class CertificateUpdateManager {
    private static final String CERT_UPDATE_URL = "/api/certificate/update";
    private static final long CERT_CHECK_INTERVAL = 24 * 60 * 60 * 1000; // 24小时

    public void schedulePeriodicCertificateCheck() {
        Observable.interval(CERT_CHECK_INTERVAL, TimeUnit.MILLISECONDS)
            .subscribeOn(Schedulers.io())
            .subscribe(tick -> checkCertificateUpdate());
    }

    private void checkCertificateUpdate() {
        try {
            // 检查服务器是否有新证书
            // 实现证书更新逻辑
            Timber.i("检查证书更新");
        } catch (Exception e) {
            Timber.e(e, "证书更新检查失败");
        }
    }
}
```

---

**实施完成后的验证清单：**

- [ ] 所有WebSocket连接使用wss://协议
- [ ] SSL证书验证正常工作
- [ ] 回退机制在SSL失败时正确触发
- [ ] 性能指标在可接受范围内
- [ ] 安全日志记录完整
- [ ] 监控指标正常上报
- [ ] 用户体验无明显影响

**注意事项：**
1. 在生产环境部署前，务必在测试环境充分验证
2. 准备好快速回滚方案，以防出现严重问题
3. 密切监控部署后的系统表现和用户反馈
4. 定期更新SSL证书，避免过期导致的连接问题
