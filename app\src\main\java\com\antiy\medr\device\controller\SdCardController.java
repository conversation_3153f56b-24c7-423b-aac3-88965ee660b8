package com.antiy.medr.device.controller;

import com.antiy.medr.model.StatusEnum;

import java.util.List;

import ga.mdm.PolicyManager;

/**
 * Created on 2022/4/7.
 *
 * <AUTHOR>
 */
public class SdCardController implements IController<String> {
    @Override
    public void enable() {
        PolicyManager.getInstance().setExternalSDPolicis(1);
    }

    @Override
    public void disable() {
        PolicyManager.getInstance().setExternalSDPolicis(DISABLE);
    }
    @Override
    public void other(StatusEnum status) {
        if (status == StatusEnum.READ_WRITE) {
            PolicyManager.getInstance().setExternalSDPolicis(2);
        }
    }
    @Override
    public boolean setWhiteList(List<String> whiteList) {
        return true;
    }
}
