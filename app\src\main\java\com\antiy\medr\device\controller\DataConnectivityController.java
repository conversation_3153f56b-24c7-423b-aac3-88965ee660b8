package com.antiy.medr.device.controller;

import com.antiy.medr.device.DevInterfaceManager;

import java.util.List;

/**
 * Created on 2022/4/7.
 *
 * <AUTHOR>
 */
public class DataConnectivityController implements IController<String>{
    @Override
    public void enable() {
        DevInterfaceManager.getInstance().setDataConnectivityPolicies(1);
    }

    @Override
    public void disable() {
        DevInterfaceManager.getInstance().setDataConnectivityPolicies(DISABLE);
    }

    @Override
    public boolean setWhiteList(List<String> whiteList) {
        return true;
    }

}
