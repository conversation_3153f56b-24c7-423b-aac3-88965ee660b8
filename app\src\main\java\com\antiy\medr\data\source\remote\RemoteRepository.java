package com.antiy.medr.data.source.remote;

import com.antiy.medr.auth.AuthApiManager;
import com.antiy.medr.data.db.AppDatabase;
import com.antiy.medr.data.source.IRepository;
import com.antiy.medr.model.ConfigModel;
import com.antiy.medr.model.EventLog;
import com.antiy.medr.network.WebSocketManager;
import com.blankj.utilcode.util.GsonUtils;
import com.cetc30.auth.sdk.api.v3.AgentAPI;
import com.cetc30.auth.sdk.api.v3.AgentAPIV3;

import java.util.List;

import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Single;

/**
 * Created on 2022/4/14.
 *
 * <AUTHOR>
 */
public class RemoteRepository implements IRepository {
    @Override
    public Completable saveConfig(ConfigModel configModel) {
        return null;
    }

    @Override
    public Observable<ConfigModel> getConfig() {
        return null;
    }

    @Override
    public Observable<Boolean> uploadLog(EventLog log) {
        return Observable.create(emitter -> {
            final Boolean success = WebSocketManager.getInstance().send(GsonUtils.toJson(log)).onErrorReturnItem(false).blockingFirst();
            if (Boolean.TRUE.equals(success)) {
                emitter.onNext(true);
            } else {
                emitter.onComplete();
            }
        });
    }

    @Override
    public Observable<String> getWsIp() {
        return null;
    }

    @Override
    public Completable saveWsIp(String url) {
        return null;
    }

    @Override
    public Single<List<EventLog>> getAllEventLogs() {
        return AppDatabase.getInstance()
                .eventLogDao()
                .queryAll();
    }

    @Override
    public Completable deleteEventLog(EventLog... logs) {
        return null;
    }

    @Override
    public Completable eventLog(EventLog log) {
        return Completable.create(emitter -> {
            AgentAPIV3.getInstance().sendCollectionInfo("终端安全管控", log.getName(), log.getTime(),  log.getDesc());
            emitter.onComplete();
        });
    }

}
