package com.antiy.medr.auth;

import android.os.Environment;

import com.antiy.medr.app.App;
import com.antiy.medr.data.source.Repository;
import com.antiy.medr.device.DevInterfaceManager;
import com.antiy.medr.model.EventEnum;
import com.antiy.medr.model.EventLog;
import com.blankj.utilcode.util.TimeUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.Utils;
import com.cetc30.auth.sdk.api.AgentAPIFactory;
import com.cetc30.auth.sdk.api.v3.AgentAPI;
import com.cetc30.auth.sdk.api.v3.AgentAPIV3;
import com.cetc30.auth.sdk.bean.UserInfo;
import com.cetc30.auth.sdk.listener.AuthListener;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * Created on 2022/6/10.
 *
 * <AUTHOR>
 */
public class AuthApiManager {

    private final AgentAPIV3 mAgentAPIV3;

    private AuthApiManager() {
        mAgentAPIV3 = AgentAPIFactory.getApiV3(Utils.getApp(), Utils.getApp().getPackageName(), null, new AuthListener() {
            @Override
            public void auth(boolean b, String s, String s1, UserInfo userInfo) {

            }

            @Override
            public void logout(boolean b, String s) {

            }

            @Override
            public void screenLock(boolean b, String s) {
                App.getInstance().setBootLocked(b);

            }

            @Override
            public void getUserInfo(UserInfo userInfo) {

            }

            @Override
            public void getToken(Object o) {

            }

            @Override
            public void destroy(boolean b, String s) {

                ToastUtils.showShort("数据销毁");
                Repository.getInstance()
                            .eventLog(new EventLog(EventEnum.WIPE_DATA, "数据销毁","数据销毁", TimeUtils.getNowString()))
                            .subscribeOn(Schedulers.io())
                            .subscribe();
                DevInterfaceManager.getInstance().wipeDeviceData();

            }
        });
    }

    public static AuthApiManager getInstance() {
        return AuthApiHolder.INSTANCE;
    }

    public AgentAPI getApi() {
        return mAgentAPIV3;
    }

    private static class AuthApiHolder {
        private static final AuthApiManager INSTANCE = new AuthApiManager();
    }
}
