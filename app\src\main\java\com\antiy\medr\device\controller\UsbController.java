package com.antiy.medr.device.controller;

import com.antiy.medr.device.DevInterfaceManager;
import com.antiy.medr.model.StatusEnum;
import com.blankj.utilcode.util.ToastUtils;

import java.util.List;

import ga.mdm.PolicyManager;

/**
 * 外围设备控制
 * Created on 2022/4/7.
 *
 * <AUTHOR>
 */
public class UsbController implements IController<String> {

    private static final int READ_WRITE = 2;

    @Override
    public void enable() {
        PolicyManager.getInstance().setExternalUsbPolicis(1);
    }

    @Override
    public void disable() {
        PolicyManager.getInstance().setExternalUsbPolicis(DISABLE);
    }

    @Override
    public void other(StatusEnum status) {
        if (status == StatusEnum.READ_WRITE) {
            PolicyManager.getInstance().setExternalUsbPolicis(READ_WRITE);
        }
    }

    @Override
    public boolean setWhiteList(List<String> whiteList) {
        final StringBuilder sb = new StringBuilder();
        final int size = whiteList.size();


        for (int i = 0; i < size; i++) {
            sb.append(whiteList.get(i));
            if (i < size - 1) {
                sb.append(",");
            }
        }

//        ToastUtils.showLong("usb 白名单取值："+externalStoragePolicies);
        boolean result =  PolicyManager.getInstance().setWhiteListedPidAndVid(sb.toString());

        return result;
    }

}
