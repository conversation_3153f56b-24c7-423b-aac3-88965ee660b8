package com.antiy.medr.ui;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.PowerManager;
import android.provider.Settings;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.ActionBar;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.recyclerview.widget.GridLayoutManager;

import com.antiy.medr.Constants;
import com.antiy.medr.R;
import com.antiy.medr.app.App;
import com.antiy.medr.auth.AuthApiManager;
import com.antiy.medr.base.AvlException;
import com.antiy.medr.base.BaseActivity;
import com.antiy.medr.base.DefaultObserver;
import com.antiy.medr.data.source.Repository;
import com.antiy.medr.databinding.ActivityMainBinding;
import com.antiy.medr.device.AdminReceiver;
import com.antiy.medr.network.Api;
import com.antiy.medr.network.WebSocketManager;
import com.antiy.medr.rxpermissions3.RxPermissions;
import com.antiy.medr.util.XPluginUtil;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.RegexUtils;
import com.blankj.utilcode.util.TimeUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.cetc30.auth.sdk.utils.ServerUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.jakewharton.rxbinding4.view.RxView;
import com.rxjava.rxlife.RxLife;

//import com.tbruyelle.rxpermissions3.RxPermissions;

import org.w3c.dom.Text;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.core.SingleOnSubscribe;
import timber.log.Timber;

/**
 * <AUTHOR>
 */
public class MainActivity extends BaseActivity<ActivityMainBinding> {

    private static final int WHITE_CODE = 1;

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        final ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }
        resetOrientation();
        String version = getVersion(this);//found different
        TextView text = findViewById(R.id.version);
        text.setText("版本号："+ version);
        RxView.clicks(binding.ivSetting)
                .throttleFirst(1, TimeUnit.SECONDS)
                .to(RxLife.to(this))
                .subscribe(unit -> showWsConfigDialog());
        final List<GridButton> gridButtons = new ArrayList<>();
        gridButtons.add(new GridButton("信任服务保障", R.drawable.ic_offline_update_home, true));
        gridButtons.add(new GridButton("数据追踪溯源", R.drawable.ic_offline_update_home, true));
        gridButtons.add(new GridButton("恶意代码查杀", R.drawable.ic_scan_home, true));
        gridButtons.add(new GridButton("软件可信管控", R.drawable.ic_online_update_home, true));
        gridButtons.add(new GridButton(getString(R.string.terminal_security_control), R.drawable.ic_offline_update_home, true));
        final GridAdapter adapter = new GridAdapter(gridButtons);
        binding.recyclerView.setAdapter(adapter);
        adapter.setOnItemClickListener((adapter1, view, position) -> {
            switch (position) {
                case 0:
                    AuthApiManager.getInstance().getApi().open();
                    break;
                case 1:
                    XPluginUtil.startActivity(MainActivity.this, Constants.WATERMARK_PLUGIN_PKG, "ShowActivity");
//                    XPluginUtil.startupPlugin(MainActivity.this, Constants.WATERMARK_PLUGIN_PKG);
                    break;
                case 2:
                    XPluginUtil.startActivity(MainActivity.this, Constants.ANTIVIRUS_PLUGIN_PKG, "plugin.activity.SecurityMain");
//                    XPluginUtil.startupPlugin(MainActivity.this, Constants.ANTIVIRUS_PLUGIN_PKG);
                    break;
                case 3:
                    XPluginUtil.startActivity(MainActivity.this, Constants.KX_PLUGIN_PKG, "activity.MainActivity");
//                    final Uri uri = Uri.parse("content://com.antiy.medr.xPlugin.Provider/com.cetc30.auth/userinfo");
//                    try (Cursor cursor = getContentResolver().query(uri, null, null, null,null)) {
//                        if (cursor != null) {
//                            cursor.moveToFirst();
//                            final String val = cursor.getString(0);
//                            Toast.makeText(this, val, Toast.LENGTH_SHORT).show();
//                            Timber.d("onCreate: " + val);
//                        } else {
//                            Toast.makeText(this, "未获取到", Toast.LENGTH_SHORT).show();
//                        }
//                    } catch (Exception e) {
//                        Timber.e(e);
//                    }
                    break;
                case 4:
                    ActivityUtils.startActivity(new Intent(MainActivity.this, TerminalWatchActivity.class));
                    break;
                default:
                    break;
            }
        });
        start();
//        enableDevice();
        binding.scoreView.startAnimate();
        addIntoWhiteList();
        //不使用辅助模式
/*        if(!isAccessBillityOn()){
            Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
            this.startActivityForResult(intent,ACCESSIBILITY);
        }*/
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        resetOrientation();
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    public void requestIgnoreBatteryOptimizations() {
        try {
            Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
            intent.setData(Uri.parse("package:" + getPackageName()));
            startActivityForResult(intent, WHITE_CODE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加至省电白名单
     */
    private void addIntoWhiteList() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!isIgnoringBatteryOptimizations()) {
                requestIgnoreBatteryOptimizations();
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == WHITE_CODE) {
            addIntoWhiteList();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        binding.scoreView.resumeAnimate();
    }

    @Override
    protected void onStop() {
        super.onStop();
        binding.scoreView.pauseAnimate();
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    private boolean isIgnoringBatteryOptimizations() {
        boolean isIgnoring = false;
        PowerManager powerManager = (PowerManager) getSystemService(Context.POWER_SERVICE);
        if (powerManager != null) {
            isIgnoring = powerManager.isIgnoringBatteryOptimizations(getPackageName());
        }
        return isIgnoring;
    }

    /**
     * 显示 WebSocket 配置的 dialog
     */
    private void showWsConfigDialog() {
        final EditText editText = new EditText(MainActivity.this);
        final String localWsIp = Repository.getInstance().getWsIp()
                .blockingFirst();
        editText.setText(localWsIp);
        editText.setSelection(localWsIp.length());
        final AlertDialog alertDialog = new AlertDialog.Builder(MainActivity.this)
                .setTitle(R.string.ws_dialog_title)
                .setView(editText)
                .setCancelable(false)
                .setNegativeButton(R.string.cancel, (dialog, which) -> dialog.dismiss())
                .setPositiveButton(R.string.confirm, null)
                .create();
        alertDialog.setOnShowListener(dialog -> {
            Button positionButton = alertDialog.getButton(DialogInterface.BUTTON_POSITIVE);
            positionButton.setOnClickListener(v -> {
                final String url = editText.getText().toString();
                //检验 ws url
                Single.create((SingleOnSubscribe<Boolean>) emitter -> {
                    final boolean isValid = RegexUtils.isIP(url);
                    if (isValid) {
                        emitter.onSuccess(true);
                    } else {
                        emitter.onError(new AvlException("非法输入"));
                    }
                })
                        //校验成功则保存到磁盘缓存
                        .flatMapCompletable(aBoolean -> Repository.getInstance().saveWsIp(url))
                        .subscribe(() -> {
                                    WebSocketManager.getInstance().connect(Api.getWsLinkFromIp(url));
                                    alertDialog.dismiss();
                                },
                                throwable -> ToastUtils.showShort("非法输入"));
            });
        });
        alertDialog.show();
    }

    private void start() {
        final RxPermissions rxPermissions = new RxPermissions(this);
        rxPermissions.request(Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE)
                .to(RxLife.to(this))
                .subscribe(new DefaultObserver<Boolean>() {
                    @SuppressLint("NewApi")
                    @Override
                    public void onSuccess(Boolean granted) {
                        if (granted) {
                            Timber.d("权限已获取");
//                            loadPlugin();
                            PermissionUtils.requestDrawOverlays(new PermissionUtils.SimpleCallback() {
                                @Override
                                public void onGranted() {
                                    Timber.d("悬浮窗权限已获取");
                                }

                                @Override
                                public void onDenied() {
                                    Timber.d("悬浮窗权限未获取");
                                    start();
                                }
                            });
                        } else {
                            start();
                        }
                    }
                });
        new Thread(){
            @Override
            public void run(){
                super.run();
                int times = 0;
//                OutputStream fout  = null;
//                File externalStorage = Environment.getExternalStorageDirectory();
//                String path = externalStorage.getAbsolutePath()+"/"+"/crash/antiy_log.txt";
//                File file = new File(path);
//                if (file.exists())
//                    file.delete();
//                try {
//                    file.createNewFile();
//                } catch (IOException e) {
//                    throw new RuntimeException(e);
//                }

//                try {
//                    try {
//                        fout = new BufferedOutputStream(new FileOutputStream(file,true));
                while(!App.getInstance().getIsBootLocked()){
                    try {
                        AuthApiManager.getInstance().getApi().screenLock();
                        sleep(5000);

                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }

    //                            fout.write((String.format("AuthApiManager screenLock() is called %s times. %s \n",++times, TimeUtils.getNowString()).getBytes(StandardCharsets.UTF_8)));
                }


//                    } catch (FileNotFoundException e) {
//                        throw new RuntimeException(e);
//                    } catch (IOException e) {
//                        throw new RuntimeException(e);
//                    }
                }

//                finally {
//                    if (fout != null) {
//                        try {
//                            fout.close();
//                        } catch (IOException e) {
//                            throw new RuntimeException(e);
//                        }
//                    }
//                }
//            }
        }.start();
    }

    /**
     * 激活设备管理
     */
    private void enableDevice() {
        DevicePolicyManager dpm = (DevicePolicyManager) getSystemService(Context.DEVICE_POLICY_SERVICE);
        ComponentName componentName = new ComponentName(this, AdminReceiver.class);
        if (!dpm.isAdminActive(componentName)) {
            Intent intent = new Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN);
            intent.putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, componentName);
            intent.putExtra(DevicePolicyManager.EXTRA_ADD_EXPLANATION, "移动终端安全防护软件");
            startActivityForResult(intent, 0);
        }
    }

    /**
     * 旋转重置
     */
    private void resetOrientation() {
        if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
            binding.recyclerView.setLayoutManager(new GridLayoutManager(this, 5));
        } else {
            binding.recyclerView.setLayoutManager(new GridLayoutManager(this, 3));
        }
    }

    private static class GridAdapter extends BaseQuickAdapter<GridButton, BaseViewHolder> {
        public GridAdapter(@Nullable List<GridButton> data) {
            super(R.layout.item_grid_home, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder baseViewHolder, GridButton gridButton) {
            baseViewHolder.setText(R.id.tv_btn, gridButton.name);
            final Drawable drawable = getContext().getResources().getDrawable(gridButton.drawableId);
            drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
            baseViewHolder.setEnabled(R.id.tv_btn, gridButton.enabled);
            final Drawable wrap = DrawableCompat.wrap(drawable);
            wrap.setTint(getContext().getResources().getColor(gridButton.enabled ? R.color.spring_green_alpha_100 : android.R.color.darker_gray));
            final TextView tvName = baseViewHolder.getView(R.id.tv_btn);
            tvName.setCompoundDrawables(null, drawable, null, null);
        }
    }

    private static class GridButton {
        private String name;
        @DrawableRes
        private int drawableId;
        private boolean enabled;

        public GridButton(String name, int drawableId, boolean enabled) {
            this.name = name;
            this.drawableId = drawableId;
            this.enabled = enabled;
        }
    }

    public static String getVersion(Context context) {
        try {
            return context.getPackageManager().getPackageInfo(Constants.AUTH_PLUGIN_PKG, 0).versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return "x.x.x.x";
        }
    }


}