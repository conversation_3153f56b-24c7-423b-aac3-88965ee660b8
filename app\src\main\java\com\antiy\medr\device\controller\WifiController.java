package com.antiy.medr.device.controller;

import com.android.DevCtrl.DevInterface;
import com.antiy.medr.data.source.Repository;
import com.antiy.medr.device.DevInterfaceManager;
import com.blankj.utilcode.util.ArrayUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import io.reactivex.rxjava3.core.Maybe;
import timber.log.Timber;

/**
 * Created on 2022/4/7.
 *
 * <AUTHOR>
 */
public class WifiController implements IController<String>{
    @Override
    public void enable() {
        Maybe.fromObservable(Repository.getInstance()
                .getConfig())
                .subscribe(configModel -> {
                    final List<String> localWifiWl = configModel.getWifiWl();
                    DevInterfaceManager.getInstance().setWlanWhiteList(1, localWifiWl.toArray(new String[0]));
                });
    }

    @Override
    public void disable() {
        DevInterfaceManager.getInstance().setWlanPolicies(DISABLE);
    }

    @Override
    public boolean setWhiteList(List<String> whiteList) {
        final ArrayList<String> lowercaseList = new ArrayList<>();
        for (String bssid : whiteList) {
            lowercaseList.add(bssid.toLowerCase(Locale.ROOT));
        }
        final DevInterface devInterface = DevInterfaceManager.getInstance();
        final String[] wlanWhiteList = devInterface.getWlanWhiteList();
        final String[] localWhiteList = ArrayUtils.subArray(wlanWhiteList, 1, wlanWhiteList.length);
        if (ArrayUtils.equals(lowercaseList.toArray(new String[0]), localWhiteList)) {
            Timber.i("wifi白名单无变化");
            return true;
        }
        final String[] ans = lowercaseList.toArray(new String[0]);
        return devInterface.setWlanWhiteList(1, ans);
    }
}
