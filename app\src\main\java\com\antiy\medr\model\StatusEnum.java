package com.antiy.medr.model;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.google.gson.annotations.JsonAdapter;

import java.lang.reflect.Type;

/**
 * Created on 2022/4/14.
 *
 * <AUTHOR>
 */
@JsonAdapter(StatusAdapter.class)
public enum StatusEnum {
    /**
     * 默认值，不处理
     */
    DEFAULT(0, ""),
    /**
     * 允许使用
     */
    ENABLE(1, "已授权"),
    /**
     * 禁止使用
     */
    DISABLE(0, "未授权"),
    /**
     * only for usb control, don't ask me why, that's funny ^_^
     */
    READ_WRITE(2, "可读可写");


    private int code;
    private String desc;

    StatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static StatusEnum get(int code) {
        for (StatusEnum value : StatusEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return DEFAULT;
    }

    public String getDesc() {
        return desc;
    }

    public int getCode() {
        return code;
    }

}

class StatusAdapter implements JsonSerializer<StatusEnum>, JsonDeserializer<StatusEnum> {
    @Override
    public StatusEnum deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        return StatusEnum.get(json.getAsInt());
    }

    @Override
    public JsonElement serialize(StatusEnum src, Type typeOfSrc, JsonSerializationContext context) {
        return new JsonPrimitive(src.getCode());
    }
}
