package com.antiy.medr.device;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.antiy.medr.device.controller.BluetoothController;
import com.antiy.medr.device.controller.IController;
import com.antiy.medr.device.controller.IptablesController;
import com.antiy.medr.device.controller.LockController;
import com.antiy.medr.device.controller.SdCardController;
import com.antiy.medr.device.controller.UsbController;
import com.antiy.medr.device.controller.WifiController;
import com.antiy.medr.device.controller.WipeDataController;
import com.antiy.medr.model.ConfigModel;
import com.antiy.medr.model.StatusEnum;
import com.antiy.medr.util.CrashHandler;

import java.util.List;

/**
 * Created on 2022/4/14.
 *
 * <AUTHOR>
 */
public class DeviceConfigHelper {

    public   ConfigModel mConfigModel =null;
    private StatusEnum lastusbAuth=null;
    private  List<String> lastusbWL=null;
    private static DeviceConfigHelper deviceConfigHelper;
    public static boolean FIRST_PROCESS = true;

    private DeviceConfigHelper() {
    }

    public static DeviceConfigHelper getInstance(){
        if (deviceConfigHelper==null) {
            deviceConfigHelper=new DeviceConfigHelper();
        }
        return deviceConfigHelper;
    }


    public void process(ConfigModel configModel) {
            mConfigModel = configModel;
            config(new BluetoothController(), mConfigModel.getBluetoothAuth(), mConfigModel.getBluetoothWl());
            config(new WipeDataController(), mConfigModel.getDataStatus(), null);
            config(new LockController(), mConfigModel.getLockStatus(), null);
            config(new UsbController(), mConfigModel.getUsbAuth(), mConfigModel.getUsbWl());
            //config(new UsbController(), mConfigModel.getUsbAuth(), null);
            config(new  SdCardController(), mConfigModel.getSdAuth(), null);
            config(new WifiController(), mConfigModel.getWifiAuth(), mConfigModel.getWifiWl());
            config(new IptablesController(), null, mConfigModel.getIpWl());
    }

    private <T> void config(@NonNull IController<T> controller, @Nullable StatusEnum status, @Nullable List<T> whiteList) {

        if (status != null) {
            switch (status) {
                case ENABLE:
                    controller.enable();
                    break;
                case DISABLE:
                    controller.disable();
                    break;
                case READ_WRITE:
                    //服务端：sd和usb(未授权:0,启用：1,启用读写：2),远程锁定,蓝牙(启用：1,未授权：2)
                    if (controller instanceof SdCardController || controller instanceof UsbController) {
                        controller.other(status);
                    } else {
                        controller.disable();
                    }
                    break;
                case DEFAULT:
                    controller.disable();
                    break;
                default:
                    break;
            }
        }
        if (whiteList != null ) {
            final boolean success = controller.setWhiteList(whiteList);
        }
//        ToastUtils.showShort("设置" + (success ? "成功" : "失败"));
    }
}
