package com.antiy.medr.view;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.LinearInterpolator;

import androidx.annotation.IntDef;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.antiy.medr.R;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * @ClassName ScoreView
 * @Description
 * <AUTHOR>
 * @Date 2021/1/3
 * @Version 1.0
 */
public class ScoreView extends View {

    public static final int NORMAL = 0;
    public static final int RISK = 1;
    public static final int SAFE = 2;
    private static final int TEXT_SIZE = 80;
    private static final int SPACING = dp2px(25);
    private static final int[] safeRingColors = new int[4];
    private static final int[] riskRingColors = new int[4];

    static {
        safeRingColors[0] = Color.parseColor("#CC222853");
        safeRingColors[1] = Color.parseColor("#9900FBAA");
        safeRingColors[2] = Color.parseColor("#66222853");
        safeRingColors[3] = Color.parseColor("#FF00FBAA");

        riskRingColors[0] = Color.parseColor("#FF482444");
        riskRingColors[1] = Color.parseColor("#99FFC371");
        riskRingColors[2] = Color.parseColor("#66482444");
        riskRingColors[3] = Color.parseColor("#FFFFC371");
    }

    Matrix matrix = new Matrix();
    private int mStrokeWidth1 = dp2px(16);
    private int mStrokeWidth2 = dp2px(2);
    private int mStrokeWidth3 = dp2px(16);
    private int mStrokeWidth4 = dp2px(4);
    private RectF mRectF1;
    private RectF mRectF2;
    private RectF mRectF3;
    private RectF mRectF4;
    private Paint mOvalPaint;
    private int[] colors = new int[4];
    private Rect scoreRect = new Rect();
    private Paint mTextPaint;
    private int mCenterX;
    private int mCenterY;
    private String title = "";
    private String desc = "";
    private StateHandler mStateHandler = new NormalStateHandler();
    private Bitmap mSafeLayerOne;
    private Bitmap mSafeLayerTwo;
    private Bitmap mRiskLayerOne;
    private Bitmap mRiskLayerTwo;
    private Bitmap mBgLayerOne;
    private Bitmap mBgLayerTwo;
    private Paint mSweepPaint;
    private float progress;
    private float angle;
    private ObjectAnimator mAnimator;
    private LinearGradient mSafeShader;
    private LinearGradient mRiskShader;
    private ObjectAnimator mAngleAnimator;

    {
        mSafeLayerOne = BitmapFactory.decodeResource(getResources(), R.drawable.bg_safe_layer_one);
        mSafeLayerTwo = BitmapFactory.decodeResource(getResources(), R.drawable.bg_safe_layer_two);
        mRiskLayerOne = BitmapFactory.decodeResource(getResources(), R.drawable.bg_risk_layer_one);
        mRiskLayerTwo = BitmapFactory.decodeResource(getResources(), R.drawable.bg_risk_layer_two);
    }

    public ScoreView(Context context) {
        this(context, null);
    }

    public ScoreView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ScoreView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    private static int dp2px(final float dpValue) {
        final float scale = Resources.getSystem().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }

    public void setState(@State int state) {
        switch (state) {
            case NORMAL:
                mStateHandler = new NormalStateHandler();
                break;
            case RISK:
                mStateHandler = new RiskStateHandler();
                break;
            case SAFE:
                mStateHandler = new SafeStateHandler();
                break;
            default:
                break;
        }
        invalidate();
    }

    public void startAnimate() {
        if (mAngleAnimator != null && !mAngleAnimator.isRunning()) {
            mAngleAnimator.start();
        }
    }

    public void stopAnimate() {
        if (mAngleAnimator != null && mAngleAnimator.isRunning()) {
            mAngleAnimator.cancel();
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public void pauseAnimate() {
        if (mAngleAnimator != null && mAngleAnimator.isRunning()) {
            mAngleAnimator.pause();
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public void resumeAnimate() {
        if (mAngleAnimator != null && mAngleAnimator.isPaused()) {
            mAngleAnimator.resume();
        }
    }

    public float getProgress() {
        return progress;
    }

    public void setProgress(float progress) {
        this.progress = progress;
        invalidate();
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
        invalidate();
    }

    public float getAngle() {
        return angle;
    }

    public void setAngle(float angle) {
        this.angle = angle;
        invalidate();
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        mOvalPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mOvalPaint.setStyle(Paint.Style.STROKE);
        mOvalPaint.setStrokeWidth(mStrokeWidth1);
        mOvalPaint.setColor(safeRingColors[0]);

        mTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mTextPaint.setTextSize(TEXT_SIZE);
        mTextPaint.setColor(Color.parseColor("#FFFFFF"));
        mTextPaint.setTextAlign(Paint.Align.CENTER);

        mSweepPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mAnimator = ObjectAnimator.ofFloat(this, "progress", 0, 100)
                .setDuration(2 * 1000);
        mAnimator.setInterpolator(new LinearInterpolator());
        mAnimator.setRepeatCount(ValueAnimator.INFINITE);

        mAngleAnimator = ObjectAnimator.ofFloat(this, "angle", 0, 360);
        mAngleAnimator.setRepeatCount(ValueAnimator.INFINITE);
        mAngleAnimator.setInterpolator(new LinearInterpolator());
        mAngleAnimator.setDuration(10 * 1000);
        mAngleAnimator.start();

        colors = safeRingColors;
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mCenterX = w / 2;
        mCenterY = h / 2;
        int temp = mStrokeWidth1 / 2 + dp2px(57);
        mRectF1 = new RectF(temp, temp, w - temp, h - temp);
        temp = mStrokeWidth1 + mStrokeWidth2 / 2 + dp2px(57);
        mRectF2 = new RectF(temp, temp, w - temp, h - temp);
        temp = mStrokeWidth1 + mStrokeWidth2 + mStrokeWidth3 / 2 + dp2px(57);
        mRectF3 = new RectF(temp, temp, w - temp, h - temp);
        temp = mStrokeWidth1 + mStrokeWidth2 + mStrokeWidth3 + mStrokeWidth4 / 2 + dp2px(57);
        mRectF4 = new RectF(temp, temp, w - temp, h - temp);

        mSafeLayerOne = scaleBitmap(mSafeLayerOne, (float) w / (float) mSafeLayerOne.getWidth());
        mSafeLayerTwo = scaleBitmap(mSafeLayerTwo, (float) (w - SPACING * 2) / (float) mSafeLayerTwo.getWidth());
        mRiskLayerOne = scaleBitmap(mRiskLayerOne, (float) w / (float) mRiskLayerOne.getWidth());
        mRiskLayerTwo = scaleBitmap(mRiskLayerTwo, (float) (w - SPACING * 2) / (float) mRiskLayerTwo.getWidth());
        mBgLayerOne = mSafeLayerOne;
        mBgLayerTwo = mSafeLayerTwo;

        mRiskShader = new LinearGradient(mCenterX, temp, mCenterX, h - temp,
                Color.parseColor("#F1BD7D"),
                Color.parseColor("#EC6272"),
                Shader.TileMode.CLAMP);
        mSafeShader = new LinearGradient(mCenterX, temp, mCenterX, h - temp,
                Color.parseColor("#2C378C"),
                Color.parseColor("#00FBAA"),
                Shader.TileMode.CLAMP);
        mSweepPaint.setShader(mSafeShader);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int width = MeasureSpec.getSize(widthMeasureSpec);
        int height = MeasureSpec.getSize(heightMeasureSpec);
        int res = Math.min(width, height);
        setMeasuredDimension(res, res);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        canvas.drawBitmap(mBgLayerOne, 0, 0, mOvalPaint);
        matrix.setRotate(angle, mCenterX, mCenterY);
        matrix.preTranslate(SPACING, SPACING);
        canvas.drawBitmap(mBgLayerTwo, matrix, mOvalPaint);

        mStateHandler.render(canvas);

        mOvalPaint.setColor(colors[0]);
        mOvalPaint.setStrokeWidth(mStrokeWidth1);
        canvas.drawOval(mRectF1, mOvalPaint);
        mOvalPaint.setColor(colors[1]);
        mOvalPaint.setStrokeWidth(mStrokeWidth2);
        canvas.drawOval(mRectF2, mOvalPaint);
        mOvalPaint.setColor(colors[2]);
        mOvalPaint.setStrokeWidth(mStrokeWidth3);
        canvas.drawOval(mRectF3, mOvalPaint);
        mOvalPaint.setColor(colors[3]);
        mOvalPaint.setStrokeWidth(mStrokeWidth4);
        canvas.drawOval(mRectF4, mOvalPaint);

        if (mAnimator != null && mAnimator.isStarted()) {
            canvas.drawArc(mRectF4, 270 + progress * 3.6f, 135, true, mSweepPaint);
        }

        mTextPaint.getTextBounds(title, 0, title.length(), scoreRect);
        canvas.drawText(title, mCenterX, mCenterY, mTextPaint);
        canvas.drawText(desc, mCenterX, mCenterY + scoreRect.height(), mTextPaint);
    }

    /**
     * 按比例缩放图片
     *
     * @param origin 原图
     * @param ratio  比例
     * @return 新的bitmap
     */
    private Bitmap scaleBitmap(Bitmap origin, float ratio) {
        if (origin == null) {
            return null;
        }
        int width = origin.getWidth();
        int height = origin.getHeight();
        Matrix matrix = new Matrix();
        matrix.preScale(ratio, ratio);
        Bitmap newBM = Bitmap.createBitmap(origin, 0, 0, width, height, matrix, false);
        if (newBM.equals(origin)) {
            return newBM;
        }
        origin.recycle();
        return newBM;
    }

    @IntDef({NORMAL, RISK, SAFE})
    @Retention(RetentionPolicy.SOURCE)
    @interface State {
    }

    private interface StateHandler {
        void render(Canvas canvas);
    }

    private class NormalStateHandler implements StateHandler {

        @Override
        public void render(Canvas canvas) {

        }
    }

    private class RiskStateHandler implements StateHandler {

        @Override
        public void render(Canvas canvas) {
            mBgLayerOne = mRiskLayerOne;
            mBgLayerTwo = mRiskLayerTwo;
            colors = riskRingColors;
            mSweepPaint.setShader(mRiskShader);
        }
    }

    private class SafeStateHandler implements StateHandler {

        @Override
        public void render(Canvas canvas) {
            mBgLayerOne = mSafeLayerOne;
            mBgLayerTwo = mSafeLayerTwo;
            colors = safeRingColors;
            mSweepPaint.setShader(mSafeShader);
        }
    }
}
