package com.antiy.medr.network;

import android.os.Environment;

import java.io.File;
import java.io.FileOutputStream;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import timber.log.Timber;

/**
 * 安全事件日志记录器
 * 专门用于记录SSL/TLS相关的安全事件和连接信息
 * 
 * <AUTHOR>
 * @date 2024/12/24
 */
public class SecurityLogger {
    private static final String TAG = "SecurityLogger";
    private static final String LOG_FILE_PREFIX = "security_";
    private static final String LOG_DIR = "/sdcard/security_logs/";
    
    // 日志级别
    public static final String LEVEL_INFO = "INFO";
    public static final String LEVEL_WARN = "WARN";
    public static final String LEVEL_ERROR = "ERROR";
    
    /**
     * 记录SSL连接事件
     * @param url 连接URL
     * @param success 是否成功
     * @param duration 连接耗时
     */
    public static void logSslConnection(String url, boolean success, long duration) {
        String level = success ? LEVEL_INFO : LEVEL_ERROR;
        String event = success ? "SSL_CONNECTION_SUCCESS" : "SSL_CONNECTION_FAILED";
        
        String details = String.format(
            "URL=%s, 耗时=%dms", 
            maskSensitiveUrl(url), duration
        );
        
        logSecurityEvent(level, event, details);
    }
    
    /**
     * 记录证书信息
     * @param cert X509证书
     */
    public static void logCertificateInfo(X509Certificate cert) {
        if (cert == null) {
            logSecurityEvent(LEVEL_WARN, "CERTIFICATE_NULL", "证书为空");
            return;
        }
        
        try {
            String certInfo = String.format(
                "主题=%s, 颁发者=%s, 序列号=%s, 有效期=%s到%s, 算法=%s",
                cert.getSubjectDN().getName(),
                cert.getIssuerDN().getName(),
                cert.getSerialNumber().toString(),
                formatDate(cert.getNotBefore()),
                formatDate(cert.getNotAfter()),
                cert.getSigAlgName()
            );
            
            logSecurityEvent(LEVEL_INFO, "CERTIFICATE_INFO", certInfo);
            
            // 检查证书是否即将过期（30天内）
            long daysUntilExpiry = (cert.getNotAfter().getTime() - System.currentTimeMillis()) / (1000 * 60 * 60 * 24);
            if (daysUntilExpiry <= 30) {
                logSecurityEvent(LEVEL_WARN, "CERTIFICATE_EXPIRING", 
                    String.format("证书将在%d天后过期", daysUntilExpiry));
            }
            
        } catch (Exception e) {
            logSecurityEvent(LEVEL_ERROR, "CERTIFICATE_INFO_ERROR", 
                "记录证书信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 记录安全事件
     * @param event 事件类型
     * @param details 事件详情
     */
    public static void logSecurityEvent(String event, String details) {
        logSecurityEvent(LEVEL_INFO, event, details);
    }
    
    /**
     * 记录安全事件（带级别）
     * @param level 日志级别
     * @param event 事件类型
     * @param details 事件详情
     */
    public static void logSecurityEvent(String level, String event, String details) {
        String timestamp = getCurrentTimestamp();
        String logMessage = String.format(
            "[%s] [%s] %s: %s",
            timestamp, level, event, details
        );
        
        // 输出到Timber
        switch (level) {
            case LEVEL_ERROR:
                Timber.e(logMessage);
                break;
            case LEVEL_WARN:
                Timber.w(logMessage);
                break;
            default:
                Timber.i(logMessage);
                break;
        }
        
        // 输出到CrashHandler
        CrashHandler.addlog(logMessage);
        
        // 写入安全日志文件
        writeSecurityLog(logMessage);
        
        // 如果是严重安全事件，额外处理
        if (isCriticalSecurityEvent(event)) {
            handleCriticalSecurityEvent(event, details);
        }
    }
    
    /**
     * 记录SSL错误详情
     * @param error SSL异常
     * @param context 错误上下文
     */
    public static void logSslError(Exception error, String context) {
        String errorType = classifyError(error);
        String details = String.format(
            "上下文=%s, 错误类型=%s, 消息=%s",
            context, errorType, error.getMessage()
        );
        
        logSecurityEvent(LEVEL_ERROR, "SSL_ERROR", details);
        
        // 记录错误堆栈（仅在调试模式下）
        if (CrashHandler.is_Debug) {
            Timber.e(error, "SSL错误详细堆栈");
        }
    }
    
    /**
     * 记录主机名验证结果
     * @param hostname 主机名
     * @param verified 是否验证通过
     * @param reason 验证失败原因（如果失败）
     */
    public static void logHostnameVerification(String hostname, boolean verified, String reason) {
        String event = verified ? "HOSTNAME_VERIFIED" : "HOSTNAME_VERIFICATION_FAILED";
        String level = verified ? LEVEL_INFO : LEVEL_WARN;
        
        String details = String.format(
            "主机名=%s%s",
            maskSensitiveHostname(hostname),
            reason != null ? ", 原因=" + reason : ""
        );
        
        logSecurityEvent(level, event, details);
    }
    
    /**
     * 记录证书验证结果
     * @param success 是否验证成功
     * @param validationMode 验证模式
     * @param reason 失败原因（如果失败）
     */
    public static void logCertificateValidation(boolean success, String validationMode, String reason) {
        String event = success ? "CERTIFICATE_VALIDATED" : "CERTIFICATE_VALIDATION_FAILED";
        String level = success ? LEVEL_INFO : LEVEL_ERROR;
        
        String details = String.format(
            "验证模式=%s%s",
            validationMode,
            reason != null ? ", 原因=" + reason : ""
        );
        
        logSecurityEvent(level, event, details);
    }
    
    /**
     * 记录配置变更
     * @param configType 配置类型
     * @param oldValue 旧值
     * @param newValue 新值
     */
    public static void logConfigurationChange(String configType, String oldValue, String newValue) {
        String details = String.format(
            "配置类型=%s, 旧值=%s, 新值=%s",
            configType, 
            maskSensitiveValue(oldValue), 
            maskSensitiveValue(newValue)
        );
        
        logSecurityEvent(LEVEL_INFO, "CONFIG_CHANGED", details);
    }
    
    /**
     * 写入安全日志文件
     */
    private static void writeSecurityLog(String message) {
        try {
            if (!CrashHandler.is_Debug) {
                return; // 生产环境可能不需要写文件
            }
            
            String date = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(new Date());
            String fileName = LOG_FILE_PREFIX + date + ".log";
            
            if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
                File dir = new File(LOG_DIR);
                if (!dir.exists()) {
                    boolean created = dir.mkdirs();
                    if (!created) {
                        Timber.w("无法创建安全日志目录: %s", LOG_DIR);
                        return;
                    }
                }
                
                File logFile = new File(dir, fileName);
                FileOutputStream fos = new FileOutputStream(logFile, true);
                fos.write(("\n" + message).getBytes());
                fos.close();
                
                // 检查日志文件大小，如果过大则轮转
                checkLogRotation(logFile);
            }
        } catch (Exception e) {
            Timber.e(e, "写入安全日志失败");
        }
    }
    
    /**
     * 检查并执行日志轮转
     */
    private static void checkLogRotation(File logFile) {
        try {
            long maxSize = 10 * 1024 * 1024; // 10MB
            if (logFile.length() > maxSize) {
                // 重命名当前文件
                String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
                File rotatedFile = new File(logFile.getParent(), logFile.getName() + "." + timestamp);
                boolean renamed = logFile.renameTo(rotatedFile);
                
                if (renamed) {
                    Timber.i("安全日志已轮转: %s", rotatedFile.getName());
                } else {
                    Timber.w("安全日志轮转失败");
                }
            }
        } catch (Exception e) {
            Timber.e(e, "日志轮转检查失败");
        }
    }
    
    /**
     * 判断是否为关键安全事件
     */
    private static boolean isCriticalSecurityEvent(String event) {
        String[] criticalEvents = {
            "CERTIFICATE_VALIDATION_FAILED",
            "SSL_ERROR",
            "HOSTNAME_VERIFICATION_FAILED",
            "CERTIFICATE_EXPIRING",
            "SECURITY_BREACH_DETECTED"
        };
        
        for (String criticalEvent : criticalEvents) {
            if (event.contains(criticalEvent)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 处理关键安全事件
     */
    private static void handleCriticalSecurityEvent(String event, String details) {
        // 可以在这里添加额外的处理逻辑，比如：
        // 1. 发送告警通知
        // 2. 上报到安全监控系统
        // 3. 触发安全响应流程
        
        Timber.w("检测到关键安全事件: %s - %s", event, details);
        
        // 示例：记录到特殊的关键事件日志
        String criticalLogMessage = String.format(
            "[CRITICAL] [%s] %s: %s",
            getCurrentTimestamp(), event, details
        );
        
        CrashHandler.addlog("=== CRITICAL SECURITY EVENT ===");
        CrashHandler.addlog(criticalLogMessage);
        CrashHandler.addlog("=== END CRITICAL EVENT ===");
    }
    
    /**
     * 分类错误类型
     */
    private static String classifyError(Exception error) {
        String errorMessage = error.getMessage();
        if (errorMessage == null) {
            return "UNKNOWN";
        }
        
        errorMessage = errorMessage.toLowerCase();
        
        if (errorMessage.contains("certificate")) {
            return "CERTIFICATE";
        } else if (errorMessage.contains("hostname")) {
            return "HOSTNAME";
        } else if (errorMessage.contains("timeout")) {
            return "TIMEOUT";
        } else if (errorMessage.contains("handshake")) {
            return "HANDSHAKE";
        } else if (errorMessage.contains("protocol")) {
            return "PROTOCOL";
        } else {
            return "OTHER";
        }
    }
    
    /**
     * 掩码敏感URL信息
     */
    private static String maskSensitiveUrl(String url) {
        if (url == null) {
            return "null";
        }
        
        // 保留协议和端口，掩码IP地址的部分信息
        return url.replaceAll("(\\d+\\.\\d+\\.)(\\d+\\.\\d+)", "$1***.***");
    }
    
    /**
     * 掩码敏感主机名信息
     */
    private static String maskSensitiveHostname(String hostname) {
        if (hostname == null) {
            return "null";
        }
        
        // 如果是IP地址，掩码后两段
        if (hostname.matches("\\d+\\.\\d+\\.\\d+\\.\\d+")) {
            return hostname.replaceAll("(\\d+\\.\\d+\\.)(\\d+\\.\\d+)", "$1***.***");
        }
        
        // 如果是域名，保留顶级域名
        if (hostname.contains(".")) {
            String[] parts = hostname.split("\\.");
            if (parts.length > 2) {
                return "***." + parts[parts.length - 2] + "." + parts[parts.length - 1];
            }
        }
        
        return hostname;
    }
    
    /**
     * 掩码敏感值
     */
    private static String maskSensitiveValue(String value) {
        if (value == null || value.length() <= 4) {
            return "***";
        }
        
        return value.substring(0, 2) + "***" + value.substring(value.length() - 2);
    }
    
    /**
     * 获取当前时间戳
     */
    private static String getCurrentTimestamp() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
            .format(new Date());
    }
    
    /**
     * 格式化日期
     */
    private static String formatDate(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(date);
    }
    
    /**
     * 清理过期的日志文件
     * @param daysToKeep 保留天数
     */
    public static void cleanupOldLogs(int daysToKeep) {
        try {
            File logDir = new File(LOG_DIR);
            if (!logDir.exists()) {
                return;
            }
            
            long cutoffTime = System.currentTimeMillis() - (daysToKeep * 24 * 60 * 60 * 1000L);
            File[] logFiles = logDir.listFiles();
            
            if (logFiles != null) {
                int deletedCount = 0;
                for (File file : logFiles) {
                    if (file.isFile() && file.getName().startsWith(LOG_FILE_PREFIX) 
                        && file.lastModified() < cutoffTime) {
                        if (file.delete()) {
                            deletedCount++;
                        }
                    }
                }
                
                if (deletedCount > 0) {
                    logSecurityEvent("LOG_CLEANUP", String.format("清理了%d个过期日志文件", deletedCount));
                }
            }
            
        } catch (Exception e) {
            Timber.e(e, "清理日志文件失败");
        }
    }
}
