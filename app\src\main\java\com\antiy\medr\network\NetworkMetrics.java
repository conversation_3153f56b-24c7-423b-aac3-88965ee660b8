package com.antiy.medr.network;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import timber.log.Timber;

/**
 * 网络连接指标监控类
 * 用于统计和监控SSL连接的性能指标
 * 
 * <AUTHOR>
 * @date 2024/12/24
 */
public class NetworkMetrics {
    private static final String TAG = "NetworkMetrics";
    private static final int MAX_RECORDS = 100; // 保持最近100次记录
    
    // 连接统计
    private static final AtomicInteger sslConnectionAttempts = new AtomicInteger(0);
    private static final AtomicInteger sslConnectionSuccesses = new AtomicInteger(0);
    private static final AtomicInteger sslConnectionFailures = new AtomicInteger(0);
    
    // 时间统计
    private static final List<Long> connectionTimes = new CopyOnWriteArrayList<>();
    private static final AtomicLong totalConnectionTime = new AtomicLong(0);
    private static final AtomicLong minConnectionTime = new AtomicLong(Long.MAX_VALUE);
    private static final AtomicLong maxConnectionTime = new AtomicLong(0);
    
    // 错误统计
    private static final AtomicInteger certificateErrors = new AtomicInteger(0);
    private static final AtomicInteger hostnameErrors = new AtomicInteger(0);
    private static final AtomicInteger timeoutErrors = new AtomicInteger(0);
    private static final AtomicInteger otherErrors = new AtomicInteger(0);
    
    // 回退统计
    private static final AtomicInteger fallbackAttempts = new AtomicInteger(0);
    private static final AtomicInteger fallbackSuccesses = new AtomicInteger(0);
    
    /**
     * 记录连接尝试
     */
    public static void recordConnectionAttempt() {
        sslConnectionAttempts.incrementAndGet();
        Timber.d("记录连接尝试，总计: %d", sslConnectionAttempts.get());
    }
    
    /**
     * 记录连接成功
     * @param duration 连接耗时（毫秒）
     */
    public static void recordConnectionSuccess(long duration) {
        sslConnectionSuccesses.incrementAndGet();
        recordConnectionTime(duration);
        
        Timber.d("记录连接成功，耗时: %dms, 总成功: %d", duration, sslConnectionSuccesses.get());
    }
    
    /**
     * 记录连接失败
     * @param errorType 错误类型
     */
    public static void recordConnectionFailure(String errorType) {
        sslConnectionFailures.incrementAndGet();
        
        // 根据错误类型分类统计
        switch (errorType.toLowerCase()) {
            case "certificate":
                certificateErrors.incrementAndGet();
                break;
            case "hostname":
                hostnameErrors.incrementAndGet();
                break;
            case "timeout":
                timeoutErrors.incrementAndGet();
                break;
            default:
                otherErrors.incrementAndGet();
                break;
        }
        
        Timber.d("记录连接失败，类型: %s, 总失败: %d", errorType, sslConnectionFailures.get());
    }
    
    /**
     * 记录回退尝试
     */
    public static void recordFallbackAttempt() {
        fallbackAttempts.incrementAndGet();
        Timber.d("记录回退尝试，总计: %d", fallbackAttempts.get());
    }
    
    /**
     * 记录回退成功
     */
    public static void recordFallbackSuccess() {
        fallbackSuccesses.incrementAndGet();
        Timber.d("记录回退成功，总计: %d", fallbackSuccesses.get());
    }
    
    /**
     * 记录连接时间
     */
    private static void recordConnectionTime(long duration) {
        connectionTimes.add(duration);
        totalConnectionTime.addAndGet(duration);
        
        // 更新最小值
        long currentMin = minConnectionTime.get();
        while (duration < currentMin && !minConnectionTime.compareAndSet(currentMin, duration)) {
            currentMin = minConnectionTime.get();
        }
        
        // 更新最大值
        long currentMax = maxConnectionTime.get();
        while (duration > currentMax && !maxConnectionTime.compareAndSet(currentMax, duration)) {
            currentMax = maxConnectionTime.get();
        }
        
        // 保持最近的记录数量
        while (connectionTimes.size() > MAX_RECORDS) {
            connectionTimes.remove(0);
        }
    }
    
    /**
     * 获取连接成功率
     * @return 成功率（0.0 - 1.0）
     */
    public static double getSuccessRate() {
        int attempts = sslConnectionAttempts.get();
        int successes = sslConnectionSuccesses.get();
        return attempts > 0 ? (double) successes / attempts : 0.0;
    }
    
    /**
     * 获取平均连接时间
     * @return 平均连接时间（毫秒）
     */
    public static double getAverageConnectionTime() {
        if (connectionTimes.isEmpty()) {
            return 0.0;
        }
        
        long sum = connectionTimes.stream().mapToLong(Long::longValue).sum();
        return (double) sum / connectionTimes.size();
    }
    
    /**
     * 获取最小连接时间
     */
    public static long getMinConnectionTime() {
        long min = minConnectionTime.get();
        return min == Long.MAX_VALUE ? 0 : min;
    }
    
    /**
     * 获取最大连接时间
     */
    public static long getMaxConnectionTime() {
        return maxConnectionTime.get();
    }
    
    /**
     * 获取回退成功率
     */
    public static double getFallbackSuccessRate() {
        int attempts = fallbackAttempts.get();
        int successes = fallbackSuccesses.get();
        return attempts > 0 ? (double) successes / attempts : 0.0;
    }
    
    /**
     * 获取详细的指标报告
     */
    public static MetricsReport getDetailedReport() {
        return new MetricsReport(
            sslConnectionAttempts.get(),
            sslConnectionSuccesses.get(),
            sslConnectionFailures.get(),
            getSuccessRate(),
            getAverageConnectionTime(),
            getMinConnectionTime(),
            getMaxConnectionTime(),
            certificateErrors.get(),
            hostnameErrors.get(),
            timeoutErrors.get(),
            otherErrors.get(),
            fallbackAttempts.get(),
            fallbackSuccesses.get(),
            getFallbackSuccessRate()
        );
    }
    
    /**
     * 记录并输出指标日志
     */
    public static void logMetrics() {
        MetricsReport report = getDetailedReport();
        
        String metricsLog = String.format(
            "SSL连接指标报告:\n" +
            "  总尝试次数: %d\n" +
            "  成功次数: %d\n" +
            "  失败次数: %d\n" +
            "  成功率: %.2f%%\n" +
            "  平均连接时间: %.2fms\n" +
            "  最小连接时间: %dms\n" +
            "  最大连接时间: %dms\n" +
            "  证书错误: %d\n" +
            "  主机名错误: %d\n" +
            "  超时错误: %d\n" +
            "  其他错误: %d\n" +
            "  回退尝试: %d\n" +
            "  回退成功: %d\n" +
            "  回退成功率: %.2f%%",
            report.totalAttempts,
            report.totalSuccesses,
            report.totalFailures,
            report.successRate * 100,
            report.averageConnectionTime,
            report.minConnectionTime,
            report.maxConnectionTime,
            report.certificateErrors,
            report.hostnameErrors,
            report.timeoutErrors,
            report.otherErrors,
            report.fallbackAttempts,
            report.fallbackSuccesses,
            report.fallbackSuccessRate * 100
        );
        
        Timber.i(metricsLog);
        SecurityLogger.logSecurityEvent("METRICS_REPORT", metricsLog);
    }
    
    /**
     * 重置所有指标
     */
    public static void resetMetrics() {
        sslConnectionAttempts.set(0);
        sslConnectionSuccesses.set(0);
        sslConnectionFailures.set(0);
        connectionTimes.clear();
        totalConnectionTime.set(0);
        minConnectionTime.set(Long.MAX_VALUE);
        maxConnectionTime.set(0);
        certificateErrors.set(0);
        hostnameErrors.set(0);
        timeoutErrors.set(0);
        otherErrors.set(0);
        fallbackAttempts.set(0);
        fallbackSuccesses.set(0);
        
        Timber.i("网络指标已重置");
        SecurityLogger.logSecurityEvent("METRICS_RESET", "所有网络指标已重置");
    }
    
    /**
     * 检查连接健康状况
     * @return true表示连接状况良好
     */
    public static boolean isConnectionHealthy() {
        double successRate = getSuccessRate();
        double avgTime = getAverageConnectionTime();
        
        // 定义健康标准
        boolean healthySuccessRate = successRate >= 0.8; // 成功率 >= 80%
        boolean healthyAvgTime = avgTime <= 5000; // 平均连接时间 <= 5秒
        
        boolean isHealthy = healthySuccessRate && healthyAvgTime;
        
        if (!isHealthy) {
            String healthReport = String.format(
                "连接健康状况不佳: 成功率=%.2f%% (要求>=80%%), 平均时间=%.2fms (要求<=5000ms)",
                successRate * 100, avgTime
            );
            Timber.w(healthReport);
            SecurityLogger.logSecurityEvent("CONNECTION_UNHEALTHY", healthReport);
        }
        
        return isHealthy;
    }
    
    /**
     * 指标报告数据类
     */
    public static class MetricsReport {
        public final int totalAttempts;
        public final int totalSuccesses;
        public final int totalFailures;
        public final double successRate;
        public final double averageConnectionTime;
        public final long minConnectionTime;
        public final long maxConnectionTime;
        public final int certificateErrors;
        public final int hostnameErrors;
        public final int timeoutErrors;
        public final int otherErrors;
        public final int fallbackAttempts;
        public final int fallbackSuccesses;
        public final double fallbackSuccessRate;
        
        public MetricsReport(int totalAttempts, int totalSuccesses, int totalFailures,
                           double successRate, double averageConnectionTime,
                           long minConnectionTime, long maxConnectionTime,
                           int certificateErrors, int hostnameErrors,
                           int timeoutErrors, int otherErrors,
                           int fallbackAttempts, int fallbackSuccesses,
                           double fallbackSuccessRate) {
            this.totalAttempts = totalAttempts;
            this.totalSuccesses = totalSuccesses;
            this.totalFailures = totalFailures;
            this.successRate = successRate;
            this.averageConnectionTime = averageConnectionTime;
            this.minConnectionTime = minConnectionTime;
            this.maxConnectionTime = maxConnectionTime;
            this.certificateErrors = certificateErrors;
            this.hostnameErrors = hostnameErrors;
            this.timeoutErrors = timeoutErrors;
            this.otherErrors = otherErrors;
            this.fallbackAttempts = fallbackAttempts;
            this.fallbackSuccesses = fallbackSuccesses;
            this.fallbackSuccessRate = fallbackSuccessRate;
        }
        
        @Override
        public String toString() {
            return String.format(
                "MetricsReport{attempts=%d, successes=%d, failures=%d, successRate=%.2f%%, " +
                "avgTime=%.2fms, minTime=%dms, maxTime=%dms, certErrors=%d, hostnameErrors=%d, " +
                "timeoutErrors=%d, otherErrors=%d, fallbackAttempts=%d, fallbackSuccesses=%d, " +
                "fallbackSuccessRate=%.2f%%}",
                totalAttempts, totalSuccesses, totalFailures, successRate * 100,
                averageConnectionTime, minConnectionTime, maxConnectionTime,
                certificateErrors, hostnameErrors, timeoutErrors, otherErrors,
                fallbackAttempts, fallbackSuccesses, fallbackSuccessRate * 100
            );
        }
    }
}
