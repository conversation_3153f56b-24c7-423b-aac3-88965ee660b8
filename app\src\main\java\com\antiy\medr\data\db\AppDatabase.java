package com.antiy.medr.data.db;

/**
 * Created on 2022/4/15.
 *
 * <AUTHOR>
 */

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.antiy.medr.data.db.dao.EventLogDao;
import com.antiy.medr.model.EventLog;
import com.blankj.utilcode.util.Utils;

@Database(entities = {EventLog.class}, version = 1,exportSchema = false)
public abstract class AppDatabase extends RoomDatabase {
    public static final String DB_NAME = "data.db";

    public abstract EventLogDao eventLogDao();

    private static class AppDatabaseHolder {
        private static final AppDatabase INSTANCE = Room.databaseBuilder(Utils.getApp(), AppDatabase.class, DB_NAME)
                .fallbackToDestructiveMigration()
                .build();
    }

    public static AppDatabase getInstance() {
        return AppDatabaseHolder.INSTANCE;
    }
}
