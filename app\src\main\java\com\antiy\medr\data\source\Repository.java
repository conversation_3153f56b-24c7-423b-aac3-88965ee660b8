package com.antiy.medr.data.source;

import com.antiy.medr.data.source.local.LocalRepository;
import com.antiy.medr.data.source.remote.RemoteRepository;
import com.antiy.medr.model.ConfigModel;
import com.antiy.medr.model.EventLog;

import java.util.List;

import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.subjects.BehaviorSubject;
import io.reactivex.rxjava3.subjects.Subject;

/**
 * Created on 2022/4/2.
 *
 * <AUTHOR>
 */
public class Repository implements IRepository {

    private final @NonNull Subject<ConfigModel> mConfigSubject;
    private final @NonNull Subject<String> mIpSubject;
    private final LocalRepository mLocalRepository;
    private final RemoteRepository mRemoteRepository;

    private Repository() {
        mLocalRepository = new LocalRepository();
        mRemoteRepository = new RemoteRepository();
        //默认取本地cache中的配置
        mConfigSubject = BehaviorSubject.createDefault(mLocalRepository.getConfig().blockingFirst()).toSerialized();
        //默认取本地cache中的ip
        mIpSubject = BehaviorSubject.createDefault(mLocalRepository.getWsIp().blockingFirst()).toSerialized();
    }

    public static Repository getInstance() {
        return RepositoryHolder.INSTANCE;
    }

    /**
     * 保存配置，先从本地cache中获取配置信息，然后将需要保存的字段进行替换，同时通知配置信息观察者配置已变更
     *
     * @param configModel
     * @return
     */
    @Override
    public Completable saveConfig(ConfigModel configModel) {
        return mLocalRepository
                .getConfig()
                .flatMapCompletable(localConfig -> {
                    localConfig.compareAndSwap(configModel);
                    mConfigSubject.onNext(localConfig);
                    return mLocalRepository.saveConfig(localConfig);
                });
    }

    @Override
    public Observable<ConfigModel> getConfig() {
        return mConfigSubject;
    }

    /**
     * 保存事件日志，首选网络传输到后端，如果网络有问题则存本地数据库
     *
     * @param log
     * @return true表示以上传服务端，false表示写入本地数据库
     */
    @Override
    public Observable<Boolean> uploadLog(EventLog log) {
        return Observable.concat(mRemoteRepository.uploadLog(log), mLocalRepository.uploadLog(log))
                .firstElement()
                .toObservable();
    }

    @Override
    public Observable<String> getWsIp() {
        return mIpSubject;
    }

    @Override
    public Completable saveWsIp(String url) {
        return mLocalRepository.saveWsIp(url)
                .doOnComplete(() -> mIpSubject.onNext(url));
    }

    @Override
    public Single<List<EventLog>> getAllEventLogs() {
        return mLocalRepository.getAllEventLogs();
    }

    @Override
    public Completable deleteEventLog(EventLog... logs) {
        return mLocalRepository.deleteEventLog(logs);
    }

    @Override
    public Completable eventLog(EventLog log) {
        return mRemoteRepository.eventLog(log);
    }

    private static class RepositoryHolder {
        private static final Repository INSTANCE = new Repository();
    }
}
