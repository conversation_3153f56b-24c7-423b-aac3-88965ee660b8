package com.antiy.medr.model;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created on 2022/7/22.
 *
 * <AUTHOR>
 */
public class IptablesModel implements Parcelable {
    private String ip_name;
    private String ip_type;
    private String protocol;
    private String src_type;
    private String src_ip;
    private String src_ip_start;
    private String src_ip_end;
    private String src_port;
    private String dest_type;
    private String dest_ip;
    private String dest_ip_start;
    private String dest_ip_end;
    private String dest_port;

    public String getIp_name() {
        return ip_name;
    }

    public void setIp_name(String ip_name) {
        this.ip_name = ip_name;
    }

    public String getIp_type() {
        return ip_type;
    }

    public void setIp_type(String ip_type) {
        this.ip_type = ip_type;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getSrc_type() {
        return src_type;
    }

    public void setSrc_type(String src_type) {
        this.src_type = src_type;
    }

    public String getSrc_ip() {
        return src_ip;
    }

    public void setSrc_ip(String src_ip) {
        this.src_ip = src_ip;
    }

    public String getSrc_ip_start() {
        return src_ip_start;
    }

    public void setSrc_ip_start(String src_ip_start) {
        this.src_ip_start = src_ip_start;
    }

    public String getSrc_ip_end() {
        return src_ip_end;
    }

    public void setSrc_ip_end(String src_ip_end) {
        this.src_ip_end = src_ip_end;
    }

    public String getSrc_port() {
        return src_port;
    }

    public void setSrc_port(String src_port) {
        this.src_port = src_port;
    }

    public String getDest_type() {
        return dest_type;
    }

    public void setDest_type(String dest_type) {
        this.dest_type = dest_type;
    }

    public String getDest_ip() {
        return dest_ip;
    }

    public void setDest_ip(String dest_ip) {
        this.dest_ip = dest_ip;
    }

    public String getDest_ip_start() {
        return dest_ip_start;
    }

    public void setDest_ip_start(String dest_ip_start) {
        this.dest_ip_start = dest_ip_start;
    }

    public String getDest_ip_end() {
        return dest_ip_end;
    }

    public void setDest_ip_end(String dest_ip_end) {
        this.dest_ip_end = dest_ip_end;
    }

    public String getDest_port() {
        return dest_port;
    }

    public void setDest_port(String dest_port) {
        this.dest_port = dest_port;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {

        dest.writeString(this.ip_name);
        dest.writeString(this.ip_type);
        dest.writeString(this.protocol);
        dest.writeString(this.src_type);
        dest.writeString(this.src_ip);
        dest.writeString(this.src_ip_start);
        dest.writeString(this.src_ip_end);
        dest.writeString(this.src_port);
        dest.writeString(this.dest_type);
        dest.writeString(this.dest_ip);
        dest.writeString(this.dest_ip_start);
        dest.writeString(this.dest_ip_end);
        dest.writeString(this.dest_port);

    }

    public void readFromParcel(Parcel source) {
        this.ip_name = source.readString();
        this.ip_type = source.readString();
        this.protocol = source.readString();
        this.src_type = source.readString();
        this.src_ip = source.readString();
        this.src_ip_start = source.readString();
        this.src_ip_end = source.readString();
        this.src_port = source.readString();
        this.dest_type = source.readString();
        this.dest_ip = source.readString();
        this.dest_ip_start = source.readString();
        this.dest_ip_end = source.readString();
        this.dest_port = source.readString();
    }

    public IptablesModel() {
    }

    protected IptablesModel(Parcel in) {
        this.ip_name = in.readString();
        this.ip_type = in.readString();
        this.protocol = in.readString();
        this.src_type = in.readString();
        this.src_ip = in.readString();
        this.src_ip_start = in.readString();
        this.src_ip_end = in.readString();
        this.src_port = in.readString();
        this.dest_type = in.readString();
        this.dest_ip = in.readString();
        this.dest_ip_start = in.readString();
        this.dest_ip_end = in.readString();
        this.dest_port = in.readString();
    }

    public static final Creator<IptablesModel> CREATOR = new Creator<IptablesModel>() {
        @Override
        public IptablesModel createFromParcel(Parcel source) {
            return new IptablesModel(source);
        }

        @Override
        public IptablesModel[] newArray(int size) {
            return new IptablesModel[size];
        }
    };

    @Override
    public String toString() {

        return "IptablesModel{" +
                "ip_name='" + ip_name + '\'' +
                ", ip_type='" + ip_type + '\'' +
                ", protocol='" + protocol + '\'' +
                ", src_type='" + src_type + '\'' +
                ", src_ip='" + src_ip + '\'' +
                ", src_ip_start='" + src_ip_start + '\'' +
                ", src_ip_end='" + src_ip_end + '\'' +
                ", src_port='" + src_port + '\'' +
                ", dest_type='" + dest_type + '\'' +
                ", dest_ip='" + dest_ip + '\'' +
                ", dest_ip_start='" + dest_ip_start + '\'' +
                ", dest_ip_end='" + dest_ip_end + '\'' +
                ", dest_port='" + dest_port + '\'' +
                '}';
    }
}
