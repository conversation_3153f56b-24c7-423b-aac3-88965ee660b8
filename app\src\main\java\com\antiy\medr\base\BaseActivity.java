package com.antiy.medr.base;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.viewbinding.ViewBinding;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;

/**
 * Created on 2021/7/14.
 *
 * <AUTHOR>
 */
public abstract class BaseActivity<VB extends ViewBinding> extends AppCompatActivity implements IView {
    protected VB binding;

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView();
        initData(savedInstanceState);
    }

    private void setContentView() {
        final ParameterizedType type = (ParameterizedType) this.getClass().getGenericSuperclass();
        try {
            Class<?> clazz = (Class<?>) type.getActualTypeArguments()[0];
            final Method method = clazz.getDeclaredMethod("inflate", LayoutInflater.class);
            binding = (VB) method.invoke(null, getLayoutInflater());
            setContentView(binding.getRoot());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
