# Android项目网络安全升级实施指南

## 快速开始

本指南将帮助您将antiy-medr项目从明文通信升级为加密通信。

## 📋 实施清单

### 阶段一：准备工作

- [ ] **1.1 备份现有代码**
  ```bash
  git checkout -b feature/ssl-upgrade
  git add .
  git commit -m "备份：SSL升级前的代码状态"
  ```

- [ ] **1.2 准备SSL证书**
  - 获取服务器SSL证书文件（.crt格式）
  - 将证书文件重命名为 `server_cert.crt`
  - 放置到 `app/src/main/assets/` 目录

- [ ] **1.3 更新依赖**
  ```gradle
  // 在 app/build.gradle 中确保OkHttp版本
  implementation 'com.squareup.okhttp3:okhttp:4.12.0'
  implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
  ```

### 阶段二：添加新文件

- [ ] **2.1 添加证书管理器**
  - 复制 `CertificateManager.java` 到 `app/src/main/java/com/antiy/medr/network/`

- [ ] **2.2 添加网络指标监控**
  - 复制 `NetworkMetrics.java` 到 `app/src/main/java/com/antiy/medr/util/`

- [ ] **2.3 添加安全日志**
  - 复制 `SecurityLogger.java` 到 `app/src/main/java/com/antiy/medr/util/`

- [ ] **2.4 添加升级版WebSocket管理器**
  - 复制 `WebSocketManagerSSL.java` 到 `app/src/main/java/com/antiy/medr/network/`

### 阶段三：修改现有文件

- [ ] **3.1 更新Constants类**
  ```java
  // 在 Constants.java 中添加网络配置
  public static class Network {
      public static final boolean ENABLE_SSL = !BuildConfig.DEBUG;
      public static final String CERT_VALIDATION_STRICT = "STRICT";
      public static final String CERT_VALIDATION_PERMISSIVE = "PERMISSIVE";
      public static final String CERT_VALIDATION_DISABLED = "DISABLED";
      public static String CERT_VALIDATION_MODE = BuildConfig.DEBUG ? 
          CERT_VALIDATION_DISABLED : CERT_VALIDATION_STRICT;
      public static final boolean ENABLE_FALLBACK = true;
      public static final int MAX_SSL_RETRY_ATTEMPTS = 3;
      public static final long SSL_RETRY_DELAY_MS = 2000;
      public static final int SSL_CONNECT_TIMEOUT_SECONDS = 30;
      public static final int SSL_READ_TIMEOUT_SECONDS = 30;
      public static final int SSL_WRITE_TIMEOUT_SECONDS = 30;
      public static final String DEFAULT_PORT = "2222";
      public static final String SSL_PORT = "2223";
  }
  ```

- [ ] **3.2 更新Api类**
  ```java
  // 修改 Api.java 中的URL构建方法
  public static String getWsLinkFromIp(String ipAddress) {
      if (Constants.Network.ENABLE_SSL) {
          return "wss://" + ipAddress + ":" + Constants.Network.SSL_PORT + "/ws_init";
      } else {
          return "ws://" + ipAddress + ":" + Constants.Network.DEFAULT_PORT + "/ws_init";
      }
  }
  ```

- [ ] **3.3 替换WebSocket管理器引用**
  - 在所有使用 `WebSocketManager` 的地方替换为 `WebSocketManagerSSL`
  - 主要文件：
    - `App.java`
    - `MainActivity.java`
    - `DeviceBroadcastReceiver.java`
    - `RemoteRepository.java`

### 阶段四：配置服务器

- [ ] **4.1 服务器SSL配置**
  - 在服务器上配置SSL证书
  - 开启WSS支持（端口2223）
  - 确保防火墙允许SSL端口访问

- [ ] **4.2 测试服务器连接**
  ```bash
  # 测试WSS连接
  openssl s_client -connect your-server:2223 -servername your-server
  ```

### 阶段五：测试验证

- [ ] **5.1 单元测试**
  ```java
  // 创建测试类 CertificateManagerTest.java
  @Test
  public void testCreateTrustManager() {
      Context context = InstrumentationRegistry.getInstrumentation().getTargetContext();
      CertificateManager manager = CertificateManager.getInstance();
      X509TrustManager trustManager = manager.createTrustManager(context);
      assertNotNull("信任管理器不应为空", trustManager);
  }
  ```

- [ ] **5.2 集成测试**
  - 测试SSL WebSocket连接
  - 测试证书验证
  - 测试回退机制

- [ ] **5.3 手动测试**
  - 在开发环境测试连接
  - 验证日志输出
  - 检查指标统计

### 阶段六：部署

- [ ] **6.1 开发环境部署**
  - 设置 `CERT_VALIDATION_MODE = DISABLED`
  - 验证基本功能

- [ ] **6.2 测试环境部署**
  - 设置 `CERT_VALIDATION_MODE = PERMISSIVE`
  - 完整功能测试

- [ ] **6.3 生产环境部署**
  - 设置 `CERT_VALIDATION_MODE = STRICT`
  - 监控连接指标

## 🔧 配置选项

### 证书验证模式

```java
// 严格模式（生产环境推荐）
Constants.Network.CERT_VALIDATION_MODE = Constants.Network.CERT_VALIDATION_STRICT;

// 宽松模式（测试环境）
Constants.Network.CERT_VALIDATION_MODE = Constants.Network.CERT_VALIDATION_PERMISSIVE;

// 禁用模式（仅开发环境）
Constants.Network.CERT_VALIDATION_MODE = Constants.Network.CERT_VALIDATION_DISABLED;
```

### SSL开关控制

```java
// 启用SSL（生产环境）
Constants.Network.ENABLE_SSL = true;

// 禁用SSL（开发环境）
Constants.Network.ENABLE_SSL = false;
```

### 回退机制

```java
// 启用回退（推荐）
Constants.Network.ENABLE_FALLBACK = true;

// 禁用回退（严格安全要求）
Constants.Network.ENABLE_FALLBACK = false;
```

## 📊 监控和维护

### 查看连接指标

```java
// 在应用中添加指标查看功能
NetworkMetrics.logMetrics();
NetworkMetrics.MetricsReport report = NetworkMetrics.getDetailedReport();
```

### 查看安全日志

```bash
# 在设备上查看安全日志
adb shell cat /sdcard/security_logs/security_2024-12-24.log
```

### 定期维护任务

```java
// 在Application类中添加定期任务
Observable.interval(24, TimeUnit.HOURS)
    .subscribeOn(Schedulers.io())
    .subscribe(tick -> {
        // 清理过期日志
        SecurityLogger.cleanupOldLogs(7);
        
        // 记录指标
        NetworkMetrics.logMetrics();
        
        // 检查连接健康状况
        if (!NetworkMetrics.isConnectionHealthy()) {
            // 处理连接问题
        }
    });
```

## ⚠️ 注意事项

### 安全考虑

1. **证书管理**
   - 定期更新SSL证书
   - 监控证书过期时间
   - 使用强加密算法

2. **配置安全**
   - 生产环境必须使用严格验证模式
   - 禁用开发环境的调试功能
   - 保护敏感配置信息

3. **日志安全**
   - 避免在日志中记录敏感信息
   - 定期清理日志文件
   - 控制日志文件访问权限

### 性能考虑

1. **连接性能**
   - SSL握手会增加连接时间
   - 监控平均连接时间
   - 优化重连策略

2. **内存使用**
   - 证书缓存占用内存
   - 定期清理过期数据
   - 监控内存使用情况

### 兼容性考虑

1. **Android版本**
   - 确保目标Android版本支持所需的SSL/TLS版本
   - 测试不同Android版本的兼容性

2. **网络环境**
   - 考虑企业网络的SSL拦截
   - 提供网络问题的诊断工具

## 🚨 故障排除

### 常见问题

1. **证书验证失败**
   ```
   解决方案：
   - 检查证书文件是否正确放置
   - 验证证书是否过期
   - 确认服务器证书配置
   ```

2. **连接超时**
   ```
   解决方案：
   - 检查网络连接
   - 验证服务器SSL端口是否开放
   - 调整超时配置
   ```

3. **主机名验证失败**
   ```
   解决方案：
   - 检查证书中的主机名
   - 更新允许的主机名列表
   - 考虑使用宽松验证模式
   ```

### 调试技巧

1. **启用详细日志**
   ```java
   CrashHandler.is_Debug = true;
   ```

2. **查看SSL握手详情**
   ```bash
   adb shell setprop log.tag.SSL_HANDSHAKE VERBOSE
   ```

3. **网络抓包分析**
   ```bash
   # 使用Wireshark或tcpdump分析网络流量
   ```

## 📞 支持联系

如果在实施过程中遇到问题，请：

1. 查看详细的技术文档：`network_security_upgrade_plan.md`
2. 检查安全日志：`/sdcard/security_logs/`
3. 查看网络指标：调用 `NetworkMetrics.logMetrics()`
4. 联系技术支持团队

---

**实施完成标志：**
- [ ] 所有WebSocket连接使用wss://协议
- [ ] SSL证书验证正常工作
- [ ] 回退机制测试通过
- [ ] 性能指标在可接受范围内
- [ ] 安全日志记录完整
- [ ] 用户体验无明显影响
