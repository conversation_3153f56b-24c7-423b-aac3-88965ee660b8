pluginManagement {
    repositories {

        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }

        maven { url 'https://jitpack.io' }
        maven { url("https://repo.jenkins-ci.org/public/")}
        mavenCentral()
        google()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://jitpack.io' }
        maven { url("https://repo.jenkins-ci.org/public/")}
        google()
        mavenCentral()
    }
}
rootProject.name = "Medr"
include ':app',':xplugin'
